/**
 * Profile Integration Verification
 * 
 * This file demonstrates the key integration points between the Profile Component
 * and the BizzCorp Employee API. Use this as a reference for testing and validation.
 */

import { Component } from '@angular/core';
import { EmployeeService, BizzCorpEmployee, Department, Designation, BizzCorpRole } from '../core/services/employee.service';

@Component({
  selector: 'app-profile-verification',
  template: `
    <div class="container mt-4">
      <h2>BizzCorp Profile Integration Verification</h2>
      
      <!-- Integration Status -->
      <div class="card mb-4">
        <div class="card-header">
          <h5>Integration Status</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <div class="text-center">
                <i class="feather icon-user text-primary" style="font-size: 2rem;"></i>
                <h6 class="mt-2">Employee API</h6>
                <span class="badge" [class]="employeeLoaded ? 'bg-success' : 'bg-warning'">
                  {{ employeeLoaded ? 'Connected' : 'Loading...' }}
                </span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <i class="feather icon-briefcase text-info" style="font-size: 2rem;"></i>
                <h6 class="mt-2">Departments</h6>
                <span class="badge" [class]="departmentsLoaded ? 'bg-success' : 'bg-warning'">
                  {{ departmentsLoaded ? departmentCount + ' Loaded' : 'Loading...' }}
                </span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <i class="feather icon-award text-warning" style="font-size: 2rem;"></i>
                <h6 class="mt-2">Designations</h6>
                <span class="badge" [class]="designationsLoaded ? 'bg-success' : 'bg-warning'">
                  {{ designationsLoaded ? designationCount + ' Loaded' : 'Loading...' }}
                </span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="text-center">
                <i class="feather icon-shield text-danger" style="font-size: 2rem;"></i>
                <h6 class="mt-2">Roles</h6>
                <span class="badge" [class]="rolesLoaded ? 'bg-success' : 'bg-warning'">
                  {{ rolesLoaded ? 'Connected' : 'Loading...' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Employee Data Preview -->
      <div class="card mb-4" *ngIf="currentEmployee">
        <div class="card-header">
          <h5>Employee Data Preview</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Basic Information</h6>
              <ul class="list-unstyled">
                <li><strong>ID:</strong> {{ currentEmployee.id }}</li>
                <li><strong>Employee Code:</strong> {{ currentEmployee.employee_code }}</li>
                <li><strong>Name:</strong> {{ getFullName() }}</li>
                <li><strong>Email:</strong> {{ currentEmployee.office_email }}</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>Contact Information</h6>
              <ul class="list-unstyled">
                <li><strong>Phone (Primary):</strong> {{ currentEmployee.phone_no || 'Not provided' }}</li>
                <li><strong>Phone (Alternate):</strong> {{ currentEmployee.alternet_no || 'Not provided' }}</li>
                <li><strong>Address:</strong> {{ currentEmployee.address || 'Not provided' }}</li>
              </ul>
            </div>
          </div>
          
          <div class="row mt-3">
            <div class="col-md-6">
              <h6>Work Information</h6>
              <ul class="list-unstyled">
                <li><strong>Department ID:</strong> {{ currentEmployee.department_id || 'Not assigned' }}</li>
                <li><strong>Designation ID:</strong> {{ currentEmployee.designation_id || 'Not assigned' }}</li>
                <li><strong>Sub-Role ID:</strong> {{ currentEmployee.sub_role_id || 'Not assigned' }}</li>
                <li><strong>Role:</strong> {{ currentEmployee.role || 'Not specified' }}</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>Resolved Names</h6>
              <ul class="list-unstyled">
                <li><strong>Department Name:</strong> {{ resolvedDepartmentName || 'Resolving...' }}</li>
                <li><strong>Designation Name:</strong> {{ resolvedDesignationName || 'Resolving...' }}</li>
                <li><strong>Sub-Role Name:</strong> {{ resolvedRoleName || 'Resolving...' }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- API Endpoints Tested -->
      <div class="card mb-4">
        <div class="card-header">
          <h5>API Endpoints Tested</h5>
        </div>
        <div class="card-body">
          <div class="list-group">
            <div class="list-group-item d-flex justify-content-between align-items-center">
              <span>GET /api/v1/employees/{{ employeeId }}</span>
              <span class="badge" [class]="employeeLoaded ? 'bg-success' : 'bg-secondary'">
                {{ employeeLoaded ? 'Success' : 'Pending' }}
              </span>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
              <span>GET /api/v1/master/departments/</span>
              <span class="badge" [class]="departmentsLoaded ? 'bg-success' : 'bg-secondary'">
                {{ departmentsLoaded ? 'Success' : 'Pending' }}
              </span>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center">
              <span>GET /api/v1/master/designations/</span>
              <span class="badge" [class]="designationsLoaded ? 'bg-success' : 'bg-secondary'">
                {{ designationsLoaded ? 'Success' : 'Pending' }}
              </span>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center" *ngIf="currentEmployee?.designation_id">
              <span>GET /api/v1/master/designations/{{ currentEmployee.designation_id }}</span>
              <span class="badge" [class]="designationDetailsLoaded ? 'bg-success' : 'bg-secondary'">
                {{ designationDetailsLoaded ? 'Success' : 'Pending' }}
              </span>
            </div>
            <div class="list-group-item d-flex justify-content-between align-items-center" *ngIf="currentEmployee?.sub_role_id">
              <span>GET /api/v1/roles/{{ currentEmployee.sub_role_id }}</span>
              <span class="badge" [class]="roleDetailsLoaded ? 'bg-success' : 'bg-secondary'">
                {{ roleDetailsLoaded ? 'Success' : 'Pending' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Field Mapping Verification -->
      <div class="card">
        <div class="card-header">
          <h5>Field Mapping Verification</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-sm">
              <thead>
                <tr>
                  <th>API Field</th>
                  <th>Display Field</th>
                  <th>Value</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><code>employee_code</code></td>
                  <td>Employee Code</td>
                  <td>{{ currentEmployee?.employee_code || 'N/A' }}</td>
                  <td><span class="badge" [class]="currentEmployee?.employee_code ? 'bg-success' : 'bg-warning'">{{ currentEmployee?.employee_code ? 'Mapped' : 'Missing' }}</span></td>
                </tr>
                <tr>
                  <td><code>first_name</code></td>
                  <td>First Name</td>
                  <td>{{ currentEmployee?.first_name || 'N/A' }}</td>
                  <td><span class="badge" [class]="currentEmployee?.first_name ? 'bg-success' : 'bg-warning'">{{ currentEmployee?.first_name ? 'Mapped' : 'Missing' }}</span></td>
                </tr>
                <tr>
                  <td><code>phone_no</code></td>
                  <td>Phone Number (Primary)</td>
                  <td>{{ currentEmployee?.phone_no || 'N/A' }}</td>
                  <td><span class="badge" [class]="currentEmployee?.phone_no ? 'bg-success' : 'bg-warning'">{{ currentEmployee?.phone_no ? 'Mapped' : 'Missing' }}</span></td>
                </tr>
                <tr>
                  <td><code>alternet_no</code></td>
                  <td>Alternate Number</td>
                  <td>{{ currentEmployee?.alternet_no || 'N/A' }}</td>
                  <td><span class="badge" [class]="currentEmployee?.alternet_no ? 'bg-success' : 'bg-info'">{{ currentEmployee?.alternet_no ? 'Mapped' : 'Optional' }}</span></td>
                </tr>
                <tr>
                  <td><code>department_id</code></td>
                  <td>Department Name</td>
                  <td>{{ resolvedDepartmentName || 'N/A' }}</td>
                  <td><span class="badge" [class]="resolvedDepartmentName ? 'bg-success' : 'bg-warning'">{{ resolvedDepartmentName ? 'Resolved' : 'Pending' }}</span></td>
                </tr>
                <tr>
                  <td><code>designation_id</code></td>
                  <td>Designation Name</td>
                  <td>{{ resolvedDesignationName || 'N/A' }}</td>
                  <td><span class="badge" [class]="resolvedDesignationName ? 'bg-success' : 'bg-warning'">{{ resolvedDesignationName ? 'Resolved' : 'Pending' }}</span></td>
                </tr>
                <tr>
                  <td><code>sub_role_id</code></td>
                  <td>Sub-Role Name</td>
                  <td>{{ resolvedRoleName || 'N/A' }}</td>
                  <td><span class="badge" [class]="resolvedRoleName ? 'bg-success' : 'bg-info'">{{ resolvedRoleName ? 'Resolved' : 'Optional' }}</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `
})
export class ProfileIntegrationVerification {
  employeeId = 'DYNAMIC'; // Will be resolved from authentication
  
  // Data
  currentEmployee: BizzCorpEmployee | null = null;
  departments: Department[] = [];
  designations: Designation[] = [];
  
  // Resolved names
  resolvedDepartmentName = '';
  resolvedDesignationName = '';
  resolvedRoleName = '';
  
  // Loading states
  employeeLoaded = false;
  departmentsLoaded = false;
  designationsLoaded = false;
  rolesLoaded = false;
  designationDetailsLoaded = false;
  roleDetailsLoaded = false;
  
  // Counts
  departmentCount = 0;
  designationCount = 0;

  constructor(private employeeService: EmployeeService) {
    this.verifyIntegration();
  }

  private verifyIntegration(): void {
    // Test employee profile loading
    this.employeeService.getBizzCorpEmployeeProfile(this.employeeId).subscribe({
      next: (employee) => {
        this.currentEmployee = employee;
        this.employeeLoaded = true;
        console.log('✅ Employee profile loaded:', employee);
        
        // Test related data resolution
        this.resolveRelatedData(employee);
      },
      error: (error) => {
        console.error('❌ Employee profile failed:', error);
      }
    });

    // Test departments loading
    this.employeeService.getDepartmentsMasterData().subscribe({
      next: (departments) => {
        this.departments = departments;
        this.departmentsLoaded = true;
        this.departmentCount = departments.length;
        console.log('✅ Departments loaded:', departments.length);
      },
      error: (error) => {
        console.error('❌ Departments failed:', error);
      }
    });

    // Test designations loading
    this.employeeService.getDesignationsMasterData().subscribe({
      next: (designations) => {
        this.designations = designations;
        this.designationsLoaded = true;
        this.designationCount = designations.length;
        console.log('✅ Designations loaded:', designations.length);
      },
      error: (error) => {
        console.error('❌ Designations failed:', error);
      }
    });
  }

  private resolveRelatedData(employee: BizzCorpEmployee): void {
    // Resolve department name
    if (employee.department_id) {
      const department = this.departments.find(d => d.id === employee.department_id);
      if (department) {
        this.resolvedDepartmentName = department.name;
      }
    }

    // Test designation details loading
    if (employee.designation_id) {
      this.employeeService.getDesignationById(employee.designation_id).subscribe({
        next: (designation) => {
          this.resolvedDesignationName = designation.name;
          this.designationDetailsLoaded = true;
          console.log('✅ Designation details loaded:', designation);
        },
        error: (error) => {
          console.error('❌ Designation details failed:', error);
        }
      });
    }

    // Test role details loading
    if (employee.sub_role_id) {
      this.employeeService.getRoleById(employee.sub_role_id).subscribe({
        next: (role) => {
          this.resolvedRoleName = role.name;
          this.roleDetailsLoaded = true;
          this.rolesLoaded = true;
          console.log('✅ Role details loaded:', role);
        },
        error: (error) => {
          console.error('❌ Role details failed:', error);
        }
      });
    }
  }

  getFullName(): string {
    if (!this.currentEmployee) return '';
    return [
      this.currentEmployee.first_name,
      this.currentEmployee.middle_name,
      this.currentEmployee.last_name
    ].filter(Boolean).join(' ');
  }
}
