<!DOCTYPE html>
<html>
<head>
    <title>API Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .error { color: red; }
        .success { color: green; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Sales Form API Debug Test</h1>
    
    <div class="section">
        <h2>Product Types API Test</h2>
        <button onclick="testProductTypes()">Test Product Types API</button>
        <div id="productTypesResult"></div>
    </div>
    
    <div class="section">
        <h2>Sub Product Types API Test</h2>
        <input type="text" id="productTypeId" placeholder="Enter Product Type ID" />
        <button onclick="testSubProductTypes()">Test Sub Product Types API</button>
        <div id="subProductTypesResult"></div>
    </div>
    
    <div class="section">
        <h2>Sales Form Data API Test</h2>
        <button onclick="testSalesFormData()">Test Sales Form Data API</button>
        <div id="salesFormDataResult"></div>
    </div>

    <script>
        // Replace with your actual API base URL
        const API_BASE_URL = 'http://localhost:8000'; // Update this to match your backend URL
        
        async function makeApiCall(url, options = {}) {
            try {
                console.log('Making API call to:', url);
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        // Add any authentication headers if needed
                        // 'Authorization': 'Bearer your-token-here',
                        ...options.headers
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                console.error('API call failed:', error);
                return { success: false, error: error.message };
            }
        }
        
        async function testProductTypes() {
            const resultDiv = document.getElementById('productTypesResult');
            resultDiv.innerHTML = '<p>Loading...</p>';
            
            const result = await makeApiCall(`${API_BASE_URL}/api/v1/product-types/?page=1&per_page=100&is_active=true`);
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="success">✅ Success</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${result.error || 'API call failed'}</div>
                    <pre>Status: ${result.status}</pre>
                `;
            }
        }
        
        async function testSubProductTypes() {
            const productTypeId = document.getElementById('productTypeId').value;
            if (!productTypeId) {
                alert('Please enter a Product Type ID');
                return;
            }
            
            const resultDiv = document.getElementById('subProductTypesResult');
            resultDiv.innerHTML = '<p>Loading...</p>';
            
            const result = await makeApiCall(`${API_BASE_URL}/api/v1/sub-product-types/?page=1&per_page=100&product_type_id=${productTypeId}&is_active=true`);
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="success">✅ Success</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${result.error || 'API call failed'}</div>
                    <pre>Status: ${result.status}</pre>
                `;
            }
        }
        
        async function testSalesFormData() {
            const resultDiv = document.getElementById('salesFormDataResult');
            resultDiv.innerHTML = '<p>Loading...</p>';
            
            // Test the combined sales form data endpoint
            const result = await makeApiCall(`${API_BASE_URL}/api/v1/sales/form-data`);
            
            if (result.success) {
                resultDiv.innerHTML = `
                    <div class="success">✅ Success</div>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="error">❌ Error: ${result.error || 'API call failed'}</div>
                    <pre>Status: ${result.status}</pre>
                `;
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            console.log('Page loaded, testing APIs...');
            testProductTypes();
        };
    </script>
</body>
</html>
