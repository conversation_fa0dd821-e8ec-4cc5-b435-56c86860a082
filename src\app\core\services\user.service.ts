import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

/**
 * User interfaces based on OpenAPI specification
 */
export interface User {
  id: string;
  email: string;
  is_active?: boolean;
  is_superuser?: boolean;
  first_name?: string;
  last_name?: string;
  created_at: string;
  updated_at?: string;
  deleted_at?: string;
  is_deleted: boolean;
}

export interface PasswordResetRequest {
  email: string;
}

export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: any;
  meta?: any;
}

/**
 * User Management Service
 * Handles user-related operations including profile management, 
 * soft/hard delete, restore, and password reset functionality.
 */
@Injectable({
  providedIn: 'root'
})
export class UserService {
  private baseUrl = `${environment.apiUrl}/api/v1/users`;

  constructor(private http: HttpClient) {}

  /**
   * Get current user profile
   * GET /api/v1/users/me
   * @returns Observable of current user data
   */
  getCurrentUser(): Observable<User> {
    return this.http.get<APIResponse<User>>(`${this.baseUrl}/me`).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Invalid response format');
      }),
      catchError(this.handleError('getCurrentUser'))
    );
  }

  /**
   * Soft delete a user (admin only)
   * DELETE /api/v1/users/{user_id}/soft
   * @param userId User ID to soft delete
   * @returns Observable of operation result
   */
  softDeleteUser(userId: string): Observable<boolean> {
    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${userId}/soft`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to soft delete user');
      }),
      catchError(this.handleError('softDeleteUser'))
    );
  }

  /**
   * Restore a soft-deleted user (admin only)
   * POST /api/v1/users/{user_id}/restore
   * @param userId User ID to restore
   * @returns Observable of restored user
   */
  restoreUser(userId: string): Observable<User> {
    return this.http.post<APIResponse<User>>(`${this.baseUrl}/${userId}/restore`, {}).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        throw new Error('Failed to restore user');
      }),
      catchError(this.handleError('restoreUser'))
    );
  }

  /**
   * Get all soft-deleted users (admin only)
   * GET /api/v1/users/deleted
   * @param skip Number of records to skip (default: 0)
   * @param limit Maximum number of records to return (default: 100)
   * @returns Observable of deleted users list
   */
  getDeletedUsers(skip: number = 0, limit: number = 100): Observable<User[]> {
    const params = new HttpParams()
      .set('skip', skip.toString())
      .set('limit', limit.toString());

    return this.http.get<APIResponse<User[]>>(`${this.baseUrl}/deleted`, { params }).pipe(
      map(response => {
        if (response.success && response.data) {
          return response.data;
        }
        return [];
      }),
      catchError(this.handleError('getDeletedUsers', []))
    );
  }

  /**
   * Permanently delete a user (admin only)
   * DELETE /api/v1/users/{user_id}/hard
   * @param userId User ID to permanently delete
   * @returns Observable of operation result
   */
  hardDeleteUser(userId: string): Observable<boolean> {
    return this.http.delete<APIResponse<boolean>>(`${this.baseUrl}/${userId}/hard`).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to hard delete user');
      }),
      catchError(this.handleError('hardDeleteUser'))
    );
  }

  /**
   * Request a password reset
   * POST /api/v1/users/password-reset
   * @param email Email address for password reset
   * @returns Observable of operation result
   */
  requestPasswordReset(email: string): Observable<boolean> {
    const request: PasswordResetRequest = { email };
    
    return this.http.post<APIResponse<boolean>>(`${this.baseUrl}/password-reset`, request).pipe(
      map(response => {
        if (response.success) {
          return response.data;
        }
        throw new Error('Failed to request password reset');
      }),
      catchError(this.handleError('requestPasswordReset'))
    );
  }

  /**
   * Error handling method
   * @param operation Name of the operation that failed
   * @param result Optional result to return as fallback
   * @returns Error handler function
   */
  private handleError<T>(operation = 'operation', result?: T) {
    return (error: any): Observable<T> => {
      console.error(`${operation} failed:`, error);

      // Log detailed error information
      console.error('Error details:', {
        status: error.status,
        statusText: error.statusText,
        url: error.url,
        message: error.message,
        error: error.error
      });

      // Return fallback result if provided
      if (result !== undefined) {
        return new Observable(observer => {
          observer.next(result);
          observer.complete();
        });
      }

      return throwError(() => error);
    };
  }
}
