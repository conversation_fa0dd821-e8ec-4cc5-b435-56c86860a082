import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  LeadCategoryService,
  LeadCategory,
  LeadCategoryStatistics
} from '../../../../core/services/lead-category.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { LeadCategoryFormComponent } from './lead-category-form/lead-category-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-lead-categories',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './lead-categories.component.html',
  styleUrls: ['./lead-categories.component.scss']
})
export class LeadCategoriesComponent implements OnInit {
  // Data properties
  leadCategories: LeadCategory[] = [];
  deletedLeadCategories: LeadCategory[] = [];
  statistics: LeadCategoryStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedPriority = '';
  selectedLevel: number | null = null;
  selectedParent = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedLeadCategories: Set<string> = new Set();
  selectAll = false;

  // Filter options
  priorityLevels: any[] = [];
  decisionMakerLevels: any[] = [];
  companySizes: any[] = [];
  currencies: string[] = [];

  constructor(
    private leadCategoryService: LeadCategoryService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadLeadCategories();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.priorityLevels = this.leadCategoryService.getPriorityLevels();
    this.decisionMakerLevels = this.leadCategoryService.getDecisionMakerLevels();
    this.companySizes = this.leadCategoryService.getCompanySizes();
    this.currencies = this.leadCategoryService.getCurrencies();
  }

  /**
   * Load lead categories with current filters
   */
  loadLeadCategories(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      priority: this.selectedPriority || undefined,
      level: this.selectedLevel || undefined,
      parent_id: this.selectedParent || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.leadCategoryService.getLeadCategoriesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedLeadCategories = response.data.filter(lc => lc.deleted_at);
            this.leadCategories = [];
          } else {
            this.leadCategories = response.data.filter(lc => !lc.deleted_at);
            this.deletedLeadCategories = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load lead categories';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load lead categories. Please try again.'
        });
      }
    });
  }

  /**
   * Load lead category statistics
   */
  loadStatistics(): void {
    this.leadCategoryService.getLeadCategoryStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search lead categories
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadLeadCategories();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadLeadCategories();
  }

  /**
   * Filter by priority
   */
  onPriorityFilter(): void {
    this.currentPage = 1;
    this.loadLeadCategories();
  }

  /**
   * Filter by level
   */
  onLevelFilter(): void {
    this.currentPage = 1;
    this.loadLeadCategories();
  }

  /**
   * Filter by parent
   */
  onParentFilter(): void {
    this.currentPage = 1;
    this.loadLeadCategories();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedLeadCategories.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadLeadCategories();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadLeadCategories();
  }

  /**
   * Open create lead category modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(LeadCategoryFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLeadCategories();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Lead category created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit lead category modal
   */
  openEditModal(leadCategory: LeadCategory): void {
    const modalRef = this.modalService.open(LeadCategoryFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.leadCategory = { ...leadCategory };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLeadCategories();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Lead category updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete lead category
   */
  deleteLeadCategory(leadCategory: LeadCategory): void {
    this.popupService.showConfirmation({
      title: 'Delete Lead Category',
      message: `Are you sure you want to delete "${leadCategory.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.leadCategoryService.deleteLeadCategory(leadCategory.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLeadCategories();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Lead category deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete lead category.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore lead category
   */
  restoreLeadCategory(leadCategory: LeadCategory): void {
    this.popupService.showConfirmation({
      title: 'Restore Lead Category',
      message: `Are you sure you want to restore "${leadCategory.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.leadCategoryService.restoreLeadCategory(leadCategory.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLeadCategories();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Lead category restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore lead category.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadLeadCategories();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Lead categories uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.leadCategoryService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'lead_categories_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadLeadCategories();
    this.loadStatistics();
  }

  /**
   * Get priority label
   */
  getPriorityLabel(priority: string): string {
    return this.leadCategoryService.getPriorityLabel(priority);
  }

  /**
   * Get priority color
   */
  getPriorityColor(priority: string): string {
    return this.leadCategoryService.getPriorityColor(priority);
  }

  /**
   * Get priority badge class
   */
  getPriorityBadgeClass(priority: string): string {
    return this.leadCategoryService.getPriorityBadgeClass(priority);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Format expected value
   */
  formatExpectedValue(value: number | undefined, currency: string = 'USD'): string {
    return this.leadCategoryService.formatExpectedValue(value, currency);
  }

  /**
   * Get qualification criteria summary
   */
  getQualificationSummary(leadCategory: LeadCategory): string {
    if (!leadCategory.qualification_criteria) {
      return 'No criteria defined';
    }

    const criteria = leadCategory.qualification_criteria;
    const parts: string[] = [];

    if (criteria.budget_range) {
      parts.push(`Budget: ${this.formatExpectedValue(criteria.budget_range.min, criteria.budget_range.currency)} - ${this.formatExpectedValue(criteria.budget_range.max, criteria.budget_range.currency)}`);
    }

    if (criteria.timeline) {
      parts.push(`Timeline: ${criteria.timeline.min_days}-${criteria.timeline.max_days} days`);
    }

    if (criteria.company_size) {
      parts.push(`Size: ${criteria.company_size}`);
    }

    return parts.length > 0 ? parts.join(', ') : 'Basic criteria';
  }

  /**
   * Get automation rules count
   */
  getAutomationRulesCount(leadCategory: LeadCategory): number {
    return (leadCategory.automation_rules?.length || 0) + (leadCategory.follow_up_rules?.length || 0);
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): LeadCategory[] {
    return this.viewMode === 'deleted' ? this.deletedLeadCategories : this.leadCategories;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByLeadCategoryId(index: number, leadCategory: LeadCategory): string {
    return leadCategory.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
