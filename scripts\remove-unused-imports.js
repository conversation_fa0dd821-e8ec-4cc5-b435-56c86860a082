#!/usr/bin/env node

/**
 * Simple script to identify and remove unused imports
 * This is a basic implementation - for production use, consider using tools like ts-unused-exports
 */

const fs = require('fs');
const path = require('path');

function findTsFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.includes('node_modules') && !item.includes('.git')) {
      findTsFiles(fullPath, files);
    } else if (item.endsWith('.ts') && !item.endsWith('.d.ts')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function analyzeImports(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  const imports = [];
  const usages = new Set();
  
  // Extract imports
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (line.startsWith('import ') && line.includes('from ')) {
      const importMatch = line.match(/import\s+(?:\{([^}]+)\}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"]/);
      if (importMatch) {
        const [, namedImports, namespaceImport, defaultImport, modulePath] = importMatch;
        
        if (namedImports) {
          // Named imports
          const names = namedImports.split(',').map(name => name.trim());
          imports.push({
            line: i,
            type: 'named',
            names,
            modulePath,
            originalLine: line
          });
        } else if (namespaceImport) {
          // Namespace import
          imports.push({
            line: i,
            type: 'namespace',
            names: [namespaceImport],
            modulePath,
            originalLine: line
          });
        } else if (defaultImport) {
          // Default import
          imports.push({
            line: i,
            type: 'default',
            names: [defaultImport],
            modulePath,
            originalLine: line
          });
        }
      }
    }
  }
  
  // Find usages (simple text search)
  const codeContent = content.replace(/\/\*[\s\S]*?\*\//g, '').replace(/\/\/.*$/gm, '');
  
  for (const importItem of imports) {
    for (const name of importItem.names) {
      // Simple regex to find usage
      const usageRegex = new RegExp(`\\b${name}\\b`, 'g');
      const matches = codeContent.match(usageRegex);
      
      // If found more than once (import + usage), it's used
      if (matches && matches.length > 1) {
        usages.add(name);
      }
    }
  }
  
  return { imports, usages, content, lines };
}

function removeUnusedImports(filePath) {
  console.log(`\n📁 Analyzing: ${filePath}`);
  
  const { imports, usages, content, lines } = analyzeImports(filePath);
  
  if (imports.length === 0) {
    console.log('  ✅ No imports found');
    return;
  }
  
  let hasChanges = false;
  const newLines = [...lines];
  
  // Process imports in reverse order to maintain line numbers
  for (let i = imports.length - 1; i >= 0; i--) {
    const importItem = imports[i];
    const unusedNames = importItem.names.filter(name => !usages.has(name));
    
    if (unusedNames.length > 0) {
      console.log(`  🗑️  Unused imports: ${unusedNames.join(', ')} from ${importItem.modulePath}`);
      
      if (unusedNames.length === importItem.names.length) {
        // Remove entire import line
        newLines.splice(importItem.line, 1);
        hasChanges = true;
      } else {
        // Remove only unused named imports
        if (importItem.type === 'named') {
          const usedNames = importItem.names.filter(name => usages.has(name));
          const newImportLine = `import { ${usedNames.join(', ')} } from '${importItem.modulePath}';`;
          newLines[importItem.line] = newImportLine;
          hasChanges = true;
        }
      }
    }
  }
  
  if (hasChanges) {
    const newContent = newLines.join('\n');
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log('  ✅ File updated');
  } else {
    console.log('  ✅ No unused imports found');
  }
}

function main() {
  const srcDir = path.join(process.cwd(), 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src directory not found. Run this script from the project root.');
    process.exit(1);
  }
  
  console.log('🔍 Finding TypeScript files...');
  const tsFiles = findTsFiles(srcDir);
  
  console.log(`📊 Found ${tsFiles.length} TypeScript files`);
  console.log('🧹 Analyzing and removing unused imports...');
  
  // Process a subset of files to avoid overwhelming output
  const filesToProcess = tsFiles.slice(0, 10);
  
  for (const file of filesToProcess) {
    try {
      removeUnusedImports(file);
    } catch (error) {
      console.error(`❌ Error processing ${file}:`, error.message);
    }
  }
  
  console.log('\n✅ Import cleanup completed!');
  console.log('\n💡 Note: This is a basic cleanup. For comprehensive analysis, consider using:');
  console.log('   - ts-unused-exports');
  console.log('   - ESLint with unused-imports rules');
  console.log('   - TypeScript compiler with noUnusedLocals/noUnusedParameters');
}

if (require.main === module) {
  main();
}

module.exports = { analyzeImports, removeUnusedImports };
