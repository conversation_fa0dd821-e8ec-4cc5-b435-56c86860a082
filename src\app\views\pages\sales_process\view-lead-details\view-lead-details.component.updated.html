<div class="row" *ngIf="leadData">
  <!-- Lead Details Card -->
  <div class="col-12 mb-4">
    <div class="card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <h5 class="card-title mb-0">Lead Details</h5>
          <div class="d-flex gap-2">
            <button class="btn btn-outline-primary" (click)="toggleEditMode()" *ngIf="!isEditMode">
              <i
                data-feather="edit"
                class="icon-sm me-1"
                appFeatherIcon
              ></i>
              Edit Details
            </button>
            <button class="btn btn-success" (click)="saveChanges()" *ngIf="isEditMode" [disabled]="isUpdating">
              <i
                *ngIf="!isUpdating"
                data-feather="check"
                class="icon-sm me-1"
                appFeatherIcon
              ></i>
              <i *ngIf="isUpdating" class="spinner-border spinner-border-sm me-1"></i>
              {{ isUpdating ? 'Saving...' : 'Save Changes' }}
            </button>
            <button class="btn btn-secondary" (click)="toggleEditMode()" *ngIf="isEditMode">
              <i
                data-feather="x"
                class="icon-sm me-1"
                appFeatherIcon
              ></i>
              Cancel
            </button>
            <button class="btn btn-primary" routerLink="/sales-list">
              <i
                data-feather="arrow-left"
                class="icon-sm me-1"
                appFeatherIcon
              ></i>
              Back to List
            </button>
          </div>
        </div>

        <div class="row g-3">
          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Lead ID</span>
              <h6 class="mb-0" *ngIf="!isEditMode">
                <span class="badge bg-light-primary text-primary">{{ leadData.leadId || 'LD001234' }}</span>
              </h6>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.leadId"
                *ngIf="isEditMode"
                placeholder="Enter Lead ID">
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Lead Category</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getLeadCategoryName() }}</h6>
              <select class="form-select form-select-sm" [(ngModel)]="editFormData.leadCategory" *ngIf="isEditMode" [disabled]="leadCategoriesLoading" (ngModelChange)="onLeadCategoryChange()">
                <option value="">Select Lead Category</option>
                <option *ngFor="let category of leadCategories" [value]="category.id">
                  {{ category.name }}
                </option>
              </select>
              <div *ngIf="leadCategoriesLoading && isEditMode" class="text-muted small mt-1">
                <i class="spinner-border spinner-border-sm me-1"></i>Loading categories...
              </div>
            </div>
          </div>

          <!-- <div class="col-md-6 col-lg-3">
            <div class="detail-item"> -->
              <!-- <span class="text-muted">Lead Name</span> -->
              <!-- <h6 class="mb-0" *ngIf="!isEditMode">{{ leadData.leadName || 'Rajesh Kumar' }}</h6> -->
              <!-- <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.leadName"
                *ngIf="isEditMode"
                placeholder="Enter Lead Name">
            </div>
          </div> -->

          <div class="col-md-6 col-lg-3" *ngIf="shouldShowSourceDropdown() || !isEditMode">
            <div class="detail-item">
              <span class="text-muted">Source</span>
              <h6 class="mb-0" *ngIf="!isEditMode">
                <span class="badge bg-light text-dark">{{ getSourceNameForDisplay() }}</span>
              </h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.sourceId" (ngModelChange)="onSourceChange()" [disabled]="sourcesLoading">
                  <option value="">{{ sourcesLoading ? 'Loading sources...' : 'Select Source' }}</option>
                  <option *ngFor="let source of sources" [value]="source.id">
                    {{ source.name }}
                  </option>
                </select>
                <div *ngIf="sourcesLoading" class="text-muted small mt-1">
                  <i class="spinner-border spinner-border-sm me-1"></i>Loading sources...
                </div>
              </div>
            </div>
          </div>

          <!-- Associate Search Dropdown (appears when Lead Category is 'Lead Data' and Source is 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateDropdown()">
            <div class="detail-item">
              <span class="text-muted">Associate</span>
              <div class="position-relative">
                <input
                  type="text"
                  class="form-control form-control-sm"
                  [(ngModel)]="associateSearchValue"
                  (input)="onAssociateSearch($event)"
                  placeholder="Search associates..."
                  [disabled]="associatesLoading"
                  autocomplete="off">

                <!-- Dropdown for filtered associates -->
                <div class="dropdown-menu show w-100" *ngIf="filteredAssociates.length > 0" style="max-height: 200px; overflow-y: auto;">
                  <div class="dropdown-item-text text-muted small" *ngIf="associatesLoading">
                    Loading associates...
                  </div>
                  <button
                    type="button"
                    class="dropdown-item"
                    *ngFor="let associate of filteredAssociates | slice:0:10"
                    (click)="onAssociateSelect(associate)">
                    <div class="fw-bold">{{ associate.associate_name }}</div>
                    <div class="text-muted small">{{ associate.company_name }} - {{ associate.location_name }}</div>
                  </button>
                  <div class="dropdown-item-text text-muted small" *ngIf="filteredAssociates.length === 0 && !associatesLoading">
                    No associates found
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Associate Name field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Associate Name</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.associateNameCategory"
                placeholder="Enter Associate Name"
                autocomplete="off">
            </div>
          </div>
          
          <!-- Associate Location field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Associate Location</span>
              <select class="form-select form-select-sm" [(ngModel)]="editFormData.associateLocationCategory" [disabled]="locationsLoading">
                <option value="">{{ locationsLoading ? 'Loading locations...' : 'Select Associate Location' }}</option>
                <option *ngFor="let location of locations" [value]="location.name">
                  {{ location.name }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Associate Sub Location field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Associate Sub Location</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.associateSubLocationCategory"
                placeholder="Enter Associate Sub Location"
                autocomplete="off">
            </div>
          </div>
          
          <!-- Professional Type field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Professional Type</span>
              <select class="form-select form-select-sm" [(ngModel)]="editFormData.professionalCategory" (ngModelChange)="onProfessionalCategoryTypeChange()" [disabled]="professionTypesLoading">
                <option value="">{{ professionTypesLoading ? 'Loading profession types...' : 'Select Professional' }}</option>
                <option *ngFor="let professionType of professionTypes" [value]="professionType.value">
                  {{ professionType.label }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Profession field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Profession</span>
              <select class="form-select form-select-sm" [(ngModel)]="editFormData.professionCategory" [disabled]="!editFormData.professionalCategory || professionsLoading">
                <option value="">{{ !editFormData.professionalCategory ? 'Select Professional Type first' : (professionsLoading ? 'Loading professions...' : 'Select Profession') }}</option>
                <option *ngFor="let profession of filteredCategoryProfessions" [value]="profession.name">
                  {{ profession.name }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Lead Name field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Lead Name</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.leadNameCategory"
                placeholder="Enter Lead Name"
                autocomplete="off">
            </div>
          </div>
          
          <!-- Lead Location field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Lead Location</span>
              <select class="form-select form-select-sm" [(ngModel)]="editFormData.leadLocationCategory" [disabled]="locationsLoading">
                <option value="">{{ locationsLoading ? 'Loading locations...' : 'Select Lead Location' }}</option>
                <option *ngFor="let location of locations" [value]="location.name">
                  {{ location.name }}
                </option>
              </select>
            </div>
          </div>
          
          <!-- Lead Sub Location field (appears when Lead Category is 'Associate' or Lead Category is 'Lead Data' with Source 'Associate') -->
          <div class="col-md-6 col-lg-3" *ngIf="isEditMode && shouldShowAssociateFields()">
            <div class="detail-item">
              <span class="text-muted">Lead Sub Location</span>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.leadSubLocationCategory"
                placeholder="Enter Lead Sub Location"
                autocomplete="off">
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Company</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ leadData.companyName || 'N/A' }}</h6>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.companyName"
                *ngIf="isEditMode"
                placeholder="Enter Company Name">
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Location</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ leadData.location || 'N/A' }}</h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.locationId" (ngModelChange)="onLocationChange()" [disabled]="locationsLoading">
                  <option value="">Select Location</option>
                  <option *ngFor="let location of locations" [value]="location.id">
                    {{ location.display_name || location.name }}
                  </option>
                </select>
                <div *ngIf="locationsLoading" class="text-muted small mt-1">
                  <i class="spinner-border spinner-border-sm me-1"></i>Loading locations...
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Sub Location</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ leadData.subLocation || 'N/A' }}</h6>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.subLocation"
                *ngIf="isEditMode"
                placeholder="Enter Sub Location">
            </div>
          </div>

           <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Handover to</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ leadData.handoverTo || 'Not Assigned' }}</h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.handoverToId" (ngModelChange)="onHandoverToChange()" [disabled]="employeesLoading">
                  <option value="">Not Assigned</option>
                  <option *ngFor="let employee of employees" [value]="employee.id">
                    {{ employee.display_name }}
                  </option>
                </select>
                <div *ngIf="employeesLoading" class="text-muted small mt-1">
                  <i class="spinner-border spinner-border-sm me-1"></i>Loading employees...
                </div>
              </div>
            </div>
          </div>

           <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Connect With Name</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getProfessionTypeName() }}</h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.connectWithId" (ngModelChange)="onConnectWithChange()" [disabled]="connectWithLoading">
                  <option value="">Select Connect With</option>
                  <option *ngFor="let connectWith of connectWithList" [value]="connectWith.id">
                    {{ connectWith.name }}
                  </option>
                </select>
                <div *ngIf="connectWithList.length === 0 && !connectWithLoading" class="text-muted small mt-1">
                  No connect with options available
                </div>
                <div *ngIf="connectWithLoading" class="text-muted small mt-1">
                  Loading connect with options...
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Mobile</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getPrimaryContactMobile() }}</h6>
              <input
                type="text"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.connectWithMobile"
                *ngIf="isEditMode"
                placeholder="Enter Mobile Number">
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Email</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getMainEmailField() }}</h6>
              <input
                type="email"
                class="form-control form-control-sm"
                [(ngModel)]="editFormData.connectWithEmail"
                *ngIf="isEditMode"
                placeholder="Enter Email Address">
            </div>
          </div>
           <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Product Type</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getProductTypeName() }}</h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.productType" (ngModelChange)="onProductTypeChange()" [disabled]="productTypesLoading">
                  <option value="">Select Product Type</option>
                  <option *ngFor="let productType of productTypes" [value]="productType.name">
                    {{ productType.name }}
                  </option>
                </select>
                <div *ngIf="productTypesLoading" class="text-muted small mt-1">
                  <i class="spinner-border spinner-border-sm me-1"></i>Loading product types...
                </div>
              </div>
            </div>
          </div>

          <div class="col-md-6 col-lg-3">
            <div class="detail-item">
              <span class="text-muted">Sub Product Type</span>
              <h6 class="mb-0" *ngIf="!isEditMode">{{ getSubProductTypeName() }}</h6>
              <div *ngIf="isEditMode">
                <select class="form-select form-select-sm" [(ngModel)]="editFormData.subProductTypeId" (ngModelChange)="onSubProductTypeIdChange()" [disabled]="subProductTypesLoading || !editFormData.productTypeId">
                  <option value="">Select Sub Product Type</option>
                  <option *ngFor="let subProductType of subProductTypes" [value]="subProductType.id">
                    {{ subProductType.name }}
                  </option>
                </select>
                <div *ngIf="subProductTypesLoading" class="text-muted small mt-1">
                  <i class="spinner-border spinner-border-sm me-1"></i>Loading sub product types...
                </div>
                <div *ngIf="!subProductTypesLoading && !editFormData.productTypeId" class="text-muted small mt-1">
                  Please select a product type first
                </div>
                <div *ngIf="!subProductTypesLoading && editFormData.productTypeId && subProductTypes.length === 0" class="text-muted small mt-1">
                  No sub-product types available for selected product type
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Dynamic Product Components -->
  <div class="row" *ngIf="isEditMode && (showProductOne || showProductTwo || showCar || showLife || showProperty)">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title mb-2">Product Details</h5>

          <!-- Display Selected Sub-Product Type -->
          <div class="mb-4">
            <div class="d-flex align-items-center p-3 rounded border"
                 [ngClass]="shouldShowSubProductType() ? 'bg-light border-primary' : 'bg-warning bg-opacity-10 border-warning'"
                 [style.border-left]="shouldShowSubProductType() ? '4px solid #0d6efd' : '4px solid #ffc107'">
              <div class="flex-shrink-0 me-3">
                <i class="fas fa-2x"
                   [ngClass]="shouldShowSubProductType() ? 'fa-clipboard-list text-primary' : 'fa-exclamation-triangle text-warning'"></i>
              </div>
              <div class="flex-grow-1">
                <h6 class="mb-1 fw-bold"
                    [ngClass]="shouldShowSubProductType() ? 'text-primary' : 'text-warning'">
                  Selected Product Type
                </h6>
                <p class="mb-0 fs-5 fw-semibold"
                   [ngClass]="shouldShowSubProductType() ? 'text-dark' : 'text-muted'">
                  {{ shouldShowSubProductType() ? getSubProductTypeDisplayName() : 'Please select a sub-product type to continue' }}
                </p>
              </div>
            </div>
          </div>

          <!-- Product One Component (Corporate Syndication) -->
          <app-product-one #productOneRef *ngIf="showProductOne"
                          [selectedProductSubType]="editFormData.subProductType"
                          (submitSales)="onProductFormSubmit($event)"></app-product-one>

          <!-- Product Two Component (Retail Syndication) -->
          <app-product-two #productTwoRef *ngIf="showProductTwo"
                          [selectedProductSubType]="editFormData.subProductType"
                          (submitSales)="onProductFormSubmit($event)"></app-product-two>

          <!-- Car Component (Insurance - CAR) -->
          <app-car #carRef *ngIf="showCar"
                  [selectedProductSubType]="editFormData.subProductType"
                  (submitSales)="onProductFormSubmit($event)"></app-car>

          <!-- Life Component (Insurance - LI) -->
          <app-life #lifeRef *ngIf="showLife"
                   [selectedProductSubType]="editFormData.subProductType"
                   (submitSales)="onProductFormSubmit($event)"></app-life>

          <!-- Property Component -->
          <app-property #propertyRef *ngIf="showProperty"
                       [selectedProductSubType]="editFormData.subProductType"
                       (submitSales)="onProductFormSubmit($event)"></app-property>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabs Navigation -->
</div>
<div class="example">
  <div ngbAccordion [closeOthers]="true">
    <div ngbAccordionItem [collapsed]="false">
      <h2 ngbAccordionHeader>
        <button ngbAccordionButton>Lead Actions</button>
      </h2>
      <div ngbAccordionCollapse>
        <div ngbAccordionBody>
          <ng-template>
            <div class="col-12 mb-4">
              
                  <h5 class="card-title mb-4">Lead Actions</h5>

                  <div class="row">
                    <div class="col-md-12">
                      <div class="mb-3 d-flex flex-wrap">
                        <div class="form-check me-4">
                          <input
                            type="radio"
                            class="form-check-input"
                            id="businessCall"
                            name="leadAction"
                            [(ngModel)]="selectedLeadAction"
                            value="businessCall"
                          />
                          <label class="form-check-label" for="businessCall"
                            >Business Call</label
                          >
                        </div>

                        <div class="form-check me-4">
                          <input
                            type="radio"
                            class="form-check-input"
                            id="handoverToOpsTeam"
                            name="leadAction"
                            [(ngModel)]="selectedLeadAction"
                            value="handoverToOpsTeam"
                          />
                          <label
                            class="form-check-label"
                            for="handoverToOpsTeam"
                            >Handover to Ops Teams</label
                          >
                        </div>

                        <div class="form-check">
                          <input
                            type="radio"
                            class="form-check-input"
                            id="reject"
                            name="leadAction"
                            [(ngModel)]="selectedLeadAction"
                            value="reject"
                          />
                          <label class="form-check-label" for="reject"
                            >Reject</label
                          >
                        </div>
                      </div>

                      <!-- Conditional input field for Handover to Ops Teams -->
                      <div
                        class="mb-3"
                        *ngIf="selectedLeadAction === 'handoverToOpsTeam'"
                      >
                        <textarea
                          class="form-control"
                          id="opsInstructions"
                          rows="3"
                          placeholder="Instruction to Ops Teams"
                          [(ngModel)]="opsInstructions"
                        ></textarea>
                      </div>

                      <!-- Buttons -->
                      <div class="d-flex justify-content-end mt-4">
                        <button
                          type="button"
                          class="btn btn-primary"
                          (click)="submitLeadAction()"
                        >
                          <i
                            data-feather="check"
                            class="icon-sm me-1"
                            appFeatherIcon
                          ></i>
                          Submit
                        </button>
                      </div>
                    </div>
                  </div>
               
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <div ngbAccordionItem [collapsed]="false">
      <h2 ngbAccordionHeader>
        <button ngbAccordionButton>Follow up Screen </button>
      </h2>
      <div ngbAccordionCollapse>
        <div ngbAccordionBody>
          <ng-template>
            <div class="col-12">
              
                  <div class="row mb-3">
                    <div class="col-12 d-flex justify-content-end">
                      <button
                        class="btn btn-outline-secondary me-2"
                        (click)="showContactsManager = !showContactsManager"
                        *ngIf="leadData?.people && leadData.people.length > 0"
                      >
                        <i
                          data-feather="users"
                          class="icon-sm me-1"
                          appFeatherIcon
                        ></i>
                        {{ showContactsManager ? "Hide Contacts" : "Manage Contacts" }}
                      </button>
                      <button
                        class="btn btn-primary me-2"
                        (click)="
                          showContactForm = !showContactForm;
                          showFollowupForm = false;
                          resetContactForm()
                        "
                      >
                        <i
                          data-feather="user-plus"
                          class="icon-sm me-1"
                          appFeatherIcon
                        ></i>
                        {{ showContactForm ? "Cancel" : "Add Contact" }}
                      </button>
                      <button
                        class="btn btn-primary"
                        (click)="
                          showFollowupForm = !showFollowupForm;
                          showContactForm = false
                        "
                      >
                        <i
                          data-feather="plus"
                          class="icon-sm me-1"
                          appFeatherIcon
                        ></i>
                        {{ showFollowupForm ? "Cancel" : "Add Follow up" }}
                      </button>
                    </div>
                  </div>

                  <!-- Contact Form -->
                  <div class="row" *ngIf="showContactForm">
                    <div class="col-12">
                      <div class="card inner-card">
                        <div class="card-body">
                          <h6 class="card-title mb-3">{{ editingContactIndex >= 0 ? 'Edit Contact' : 'Add Contact' }}</h6>
                          <div class="row">
                            <div class="col-md-6 col-lg-3">
                              <div class="mb-3">
                                <label
                                  for="contactConnectWith"
                                  class="form-label"
                                  >Connect With</label
                                >
                                <select
                                  class="form-select"
                                  id="contactConnectWith"
                                  [(ngModel)]="contactConnectWith"
                                  [disabled]="connectWithLoading"
                                >
                                  <option value="">
                                    {{ connectWithLoading ? 'Loading connect with...' : 'Select' }}
                                  </option>
                                  <option *ngFor="let connectWith of connectWithList" [value]="connectWith.name">
                                    {{ connectWith.name }}
                                  </option>
                                </select>
                              </div>
                            </div>

                            <div class="col-md-6 col-lg-3">
                              <div class="mb-3">
                                <label for="contactName" class="form-label"
                                  >Name <span class="text-danger">*</span></label
                                >
                                <input
                                  type="text"
                                  class="form-control"
                                  id="contactName"
                                  placeholder="Enter name"
                                  [(ngModel)]="contactName"
                                  required
                                />
                              </div>
                            </div>
                            <div class="col-md-6 col-lg-3">
                              <div class="mb-3">
                                <label for="contactMobileNo" class="form-label"
                                  >Mobile No <span class="text-danger">*</span></label
                                >
                                <input
                                  type="text"
                                  class="form-control"
                                  id="contactMobileNo"
                                  placeholder="Enter mobile number"
                                  [(ngModel)]="contactMobileNo"
                                  required
                                />
                              </div>
                            </div>

                            <div class="col-md-6 col-lg-3">
                              <div class="mb-3">
                                <label for="contactEmailId" class="form-label"
                                  >Email ID</label
                                >
                                <input
                                  type="email"
                                  class="form-control"
                                  id="contactEmailId"
                                  placeholder="Enter email address"
                                  [(ngModel)]="contactEmailId"
                                />
                              </div>
                            </div>

                            <div class="col-12">
                              <div class="mb-3">
                                <div class="form-check">
                                  <input
                                    class="form-check-input"
                                    type="checkbox"
                                    id="contactIsPrimary"
                                    [(ngModel)]="contactIsPrimary"
                                  />
                                  <label class="form-check-label" for="contactIsPrimary">
                                    Set as Primary Contact
                                  </label>
                                </div>
                              </div>
                            </div>

                            <div class="col-12 text-end mb-3">
                              <button
                                class="btn btn-secondary me-2"
                                (click)="showContactForm = false; resetContactForm()"
                              >
                                Cancel
                              </button>
                              <button
                                class="btn btn-primary"
                                (click)="editingContactIndex >= 0 ? updateContact() : addContact()"
                              >
                                <i
                                  data-feather="check"
                                  class="icon-sm me-1"
                                  appFeatherIcon
                                ></i>
                                {{ editingContactIndex >= 0 ? 'Update Contact' : 'Submit Contact' }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Contacts List -->
                  <div class="row" *ngIf="showContactsManager && leadData?.people && leadData.people.length > 0">
                    <div class="col-12">
                      <div class="card inner-card">
                        <div class="card-body">
                          <h6 class="card-title mb-3">
                            <i data-feather="users" class="icon-sm me-2" appFeatherIcon></i>
                            Contacts ({{ leadData.people.length }})
                          </h6>
                          <div class="table-responsive">
                            <table class="table table-sm">
                              <thead>
                                <tr>
                                  <th>Name</th>
                                  <th>Role</th>
                                  <th>Mobile</th>
                                  <th>Email</th>
                                  <th>Primary</th>
                                  <th>Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                <tr *ngFor="let contact of leadData.people; let i = index">
                                  <td>
                                    <strong>{{ contact.name !== undefined ? contact.name : 'N/A' }}</strong>
                                  </td>
                                  <td>
                                    <span class="badge bg-secondary">{{ getConnectWithNameFromId(contact.connect_with_id) }}</span>
                                  </td>
                                  <td>{{ contact.mobile !== undefined ? contact.mobile : 'N/A' }}</td>
                                  <td>{{ contact.email !== undefined ? contact.email : '' }}</td>
                                  <td>
                                    <span *ngIf="contact.is_primary_contact" class="badge bg-success">
                                      <i data-feather="check" class="icon-xs" appFeatherIcon></i>
                                      Primary
                                    </span>
                                    <span *ngIf="!contact.is_primary_contact" class="text-muted">-</span>
                                  </td>
                                  <td>
                                    <button
                                      class="btn btn-sm btn-outline-primary me-1"
                                      (click)="editContact(i)"
                                      title="Edit Contact"
                                    >
                                      <i data-feather="edit-2" class="icon-xs" appFeatherIcon></i>
                                    </button>
                                    <button
                                      class="btn btn-sm btn-outline-danger"
                                      (click)="deleteContact(i)"
                                      title="Delete Contact"
                                    >
                                      <i data-feather="trash-2" class="icon-xs" appFeatherIcon></i>
                                    </button>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Follow-up Form -->
                  <div class="row" *ngIf="showFollowupForm">
                    <div class="col-12">
                      <div class="card inner-card">
                        <div class="card-body">
                          <h6 class="card-title mb-3">Add Follow-up</h6>
                          <div class="row">
                            <div class="col-md-3">
                              <div class="mb-3">
                                <label for="followupType" class="form-label"
                                  >Follow-up Type</label
                                >
                                <select
                                  class="form-select"
                                  id="followupType"
                                  [(ngModel)]="followupType"
                                >
                                  <option value="call">Call</option>
                                  <option value="meeting">Meeting</option>
                                  <option value="email">Email</option>
                                  <option value="site-visit">Site Visit</option>
                                  <option value="other">Other</option>
                                </select>
                              </div>
                            </div>

                            <div class="col-md-3">
                              <div class="mb-3">
                                <label for="followupDate" class="form-label"
                                  >Next Follow-up Date</label
                                >
                                <input
                                  type="date"
                                  class="form-control"
                                  id="followupDate"
                                  [(ngModel)]="followupDate"
                                />
                              </div>
                            </div>

                            <div class="col-md-3">
                              <div class="mb-3">
                                <label for="followupTime" class="form-label"
                                  >Follow-up Time</label
                                >
                                <input
                                  type="time"
                                  class="form-control"
                                  id="followupTime"
                                  [(ngModel)]="followupTime"
                                />
                              </div>
                            </div>

                            <div class="col-12">
                              <div class="mb-3">
                                <label for="followupNotes" class="form-label"
                                  >Follow-up Notes</label
                                >
                                <textarea
                                  class="form-control"
                                  id="followupNotes"
                                  rows="4"
                                  placeholder="Enter details about your follow-up..."
                                  [(ngModel)]="followupNotes"
                                ></textarea>
                              </div>
                            </div>

                            <div class="col-12 text-end mb-3">
                              <button
                                class="btn btn-secondary me-2"
                                (click)="showFollowupForm = false"
                              >
                                Cancel
                              </button>
                              <button
                                class="btn btn-primary"
                                (click)="addFollowup()"
                              >
                                <i
                                  data-feather="check"
                                  class="icon-sm me-1"
                                  appFeatherIcon
                                ></i>
                                Submit Follow-up
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Activity Timeline -->
                  <div class="timeline-wrapper mt-4">
                    <div
                      class="timeline-item"
                      *ngFor="let activity of activities"
                    >
                      <div
                        class="timeline-badge"
                        [ngClass]="getActivityBadgeClass(activity.type)"
                      >
                        <i
                          [attr.data-feather]="getActivityIcon(activity.type)"
                          appFeatherIcon
                        ></i>
                      </div>
                      <div class="timeline-content">
                        <h6 class="mb-1">{{ activity.title }}</h6>

                        <!-- Special handling for contact type activities -->
                        <div *ngIf="activity.type === 'contact'" class="mb-2">
                          <p
                            class="text-muted mb-0"
                            [innerHTML]="
                              activity.description.split('\n').join('<br>')
                            "
                          ></p>
                        </div>

                        <!-- Default display for other activity types -->
                        <p
                          *ngIf="activity.type !== 'contact'"
                          class="text-muted mb-0"
                        >
                          {{ activity.description }}
                        </p>

                        <small class="text-muted">{{ activity.date }}</small>

                        <!-- Display follow-up date and time if available -->
                        <div
                          *ngIf="activity.followupDate"
                          class="mt-2 p-2 bg-light rounded"
                        >
                          <small class="d-block"
                            ><strong>Next Follow-up:</strong>
                            {{ activity.followupDate | date : "mediumDate" }} at
                            {{ activity.followupTime }}</small
                          >
                          <small
                            *ngIf="activity.alternativeNumber"
                            class="d-block"
                            ><strong>Alternative Number:</strong>
                            {{ activity.alternativeNumber }}</small
                          >
                        </div>
                      </div>
                    </div>

                    <!-- Empty state when no activities -->
                    <div
                      class="text-center py-4"
                      *ngIf="activities.length === 0"
                    >
                      <i
                        data-feather="calendar"
                        class="text-muted icon-lg mb-3"
                        appFeatherIcon
                      ></i>
                      <p class="mb-0">No activities found</p>
                      <small class="text-muted"
                        >Add a follow-up to get started</small
                      >
                    </div>
                  </div>
               
            </div>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Loading state -->
<div class="row" *ngIf="isLoading">
  <div class="col-12">
    <div class="card">
      <div class="card-body text-center py-5">
        <div class="spinner-border text-primary mb-3" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <h5>Loading Lead Details</h5>
        <p class="text-muted">
          Please wait while we fetch the lead information...
        </p>
      </div>
    </div>
  </div>
</div>

<!-- Error message if lead data is not available -->
<div class="row" *ngIf="!leadData && !isLoading">
  <div class="col-12">
    <div class="card">
      <div class="card-body text-center py-5">
        <i
          data-feather="alert-circle"
          class="text-muted icon-lg mb-3"
          appFeatherIcon
        ></i>
        <h5>Lead Not Found</h5>
        <p class="text-muted">
          The lead you're looking for doesn't exist or has been removed.
        </p>
        <button class="btn btn-primary" routerLink="/sales-list">
          Back to Sales List
        </button>
      </div>
    </div>
  </div>
</div>