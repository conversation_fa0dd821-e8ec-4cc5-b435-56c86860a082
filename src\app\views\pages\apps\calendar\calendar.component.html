<div class="row calendar-container">
  <div class="col-md-12">
    <div class="row">
      <!-- Employee Info Header -->
      <div class="col-12 grid-margin employee-info-header" *ngIf="employeeInfo">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h4 class="card-title mb-1">Calendar</h4>
                <p class="text-muted mb-0">
                  <i class="fas fa-user me-2"></i>{{ employeeInfo.name }}
                  <span class="badge bg-primary ms-2">{{ employeeInfo.code }}</span>
                  <span *ngIf="currentCalendarData" class="badge bg-info ms-2">
                    {{ currentCalendarData.month_name }} {{ currentCalendarData.year }}
                  </span>
                  <span *ngIf="currentDisplayMonth" class="badge bg-secondary ms-2">
                    Viewing: {{ getMonthName(currentDisplayMonth.month) }} {{ currentDisplayMonth.year }}
                  </span>
                  <span *ngIf="loading" class="badge bg-warning ms-2">
                    <i class="fas fa-spinner fa-spin me-1"></i>Loading...
                  </span>
                </p>
              </div>
              <div class="text-end">
                <div class="btn-group me-2" role="group">
                  <button
                    class="btn btn-outline-secondary btn-sm"
                    (click)="goToPrevMonth()"
                    [disabled]="loading || isLoadingData"
                    title="Previous Month">
                    <i class="fas fa-chevron-left"></i>
                  </button>
                  <button
                    class="btn btn-outline-secondary btn-sm"
                    (click)="goToToday()"
                    [disabled]="loading || isLoadingData"
                    title="Today">
                    <i class="fas fa-calendar-day"></i>
                  </button>
                  <button
                    class="btn btn-outline-secondary btn-sm"
                    (click)="goToNextMonth()"
                    [disabled]="loading || isLoadingData"
                    title="Next Month">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
                <button
                  class="btn btn-outline-primary btn-sm"
                  (click)="refreshCalendar()"
                  [disabled]="loading || isLoadingData">
                  <i class="fas fa-sync-alt me-1" [class.fa-spin]="loading || isLoadingData"></i>
                  Refresh
                </button>
                <button
                  class="btn btn-outline-warning btn-sm ms-1"
                  (click)="forceReloadCurrentMonth()"
                  [disabled]="loading || isLoadingData"
                  title="Force Reload (Clears Cache)">
                  <i class="fas fa-redo me-1"></i>
                  Force Reload
                </button>
                <button
                  class="btn btn-outline-info btn-sm ms-1"
                  (click)="debugCurrentMonthData()"
                  [disabled]="loading || isLoadingData"
                  title="Debug Current Month Data">
                  <i class="fas fa-bug me-1"></i>
                  Debug
                </button>
                <button
                  class="btn btn-outline-success btn-sm ms-1"
                  (click)="testHolidayDisplay()"
                  [disabled]="loading || isLoadingData"
                  title="Test Holiday Display">
                  <i class="fas fa-calendar-check me-1"></i>
                  Test Holidays
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Calendar -->
      <div class="col-12 grid-margin">
        <div class="card">
          <div class="card-body">
            <!-- Loading State -->
            <div *ngIf="loading" class="calendar-loading">
              <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3 text-muted">
                  Loading your calendar
                  <span *ngIf="loadingMonthInfo">for {{ loadingMonthInfo.month_name }} {{ loadingMonthInfo.year }}</span>
                  <span *ngIf="!loadingMonthInfo && currentCalendarData">for {{ currentCalendarData.month_name }} {{ currentCalendarData.year }}</span>...
                </p>
              </div>
            </div>

            <!-- Error State -->
            <div *ngIf="error && !loading" class="calendar-error">
              <div class="alert alert-danger" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ error }}
                <button class="btn btn-sm btn-outline-danger ms-2" (click)="refreshCalendar()">
                  Try Again
                </button>
              </div>
            </div>

            <!-- Calendar -->
            <div *ngIf="!loading && !error">
              <full-calendar
                #calendar
                [options]='calendarOptions'
              ></full-calendar>
            </div>
          </div>
        </div>
      </div>
      <!-- Event Legend -->
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <h6 class="mb-3 text-secondary">
              <i class="fas fa-info-circle me-2"></i>Event Legend
            </h6>
            <div class="row">
              <!-- Holidays -->
              <div class="col-md-4 mb-3">
                <div class="d-flex align-items-center">
                  <div class="legend-color-box bg-danger me-2"></div>
                  <div>
                    <strong>🎉 Holidays</strong>
                    <small class="d-block text-muted">Public holidays and special occasions</small>
                    <small class="text-info">
                      <i class="fas fa-database me-1"></i>{{ holidays.length }} loaded
                      <span *ngIf="holidaysError" class="text-danger ms-1">({{ holidaysError }})</span>
                    </small>
                  </div>
                </div>
              </div>

              <!-- Leave Records -->
              <div class="col-md-4 mb-3">
                <div class="d-flex align-items-center">
                  <div class="legend-color-box bg-success me-2"></div>
                  <div>
                    <strong>🏖️ Leave Records</strong>
                    <small class="d-block text-muted">ALL your leave records (approved, pending, rejected, cancelled)</small>
                    <small class="text-info">
                      <i class="fas fa-database me-1"></i>{{ userLeaves.length }} total records loaded
                      <span *ngIf="leavesError" class="text-danger ms-1">({{ leavesError }})</span>
                    </small>
                  </div>
                </div>
              </div>

              <!-- Attendance -->
              <div class="col-md-4 mb-3">
                <div class="d-flex align-items-center">
                  <div class="legend-color-box bg-primary me-2"></div>
                  <div>
                    <strong>📅 Attendance</strong>
                    <small class="d-block text-muted">Daily attendance and working hours</small>
                    <small class="text-info">
                      <i class="fas fa-calendar me-1"></i>Calendar data
                    </small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Leave Status Legend -->
            <div class="mt-3 pt-3 border-top">
              <h6 class="mb-2 text-secondary">Leave Status Colors & Counts</h6>
              <div class="row">
                <div class="col-md-3 col-6 mb-2">
                  <div class="d-flex align-items-center">
                    <div class="legend-color-box bg-success me-2"></div>
                    <small><strong>Approved</strong> <span class="badge bg-success ms-1">{{ getLeaveCountByStatus('APPROVED') }}</span></small>
                  </div>
                </div>
                <div class="col-md-3 col-6 mb-2">
                  <div class="d-flex align-items-center">
                    <div class="legend-color-box bg-warning me-2"></div>
                    <small><strong>Pending</strong> <span class="badge bg-warning text-dark ms-1">{{ getLeaveCountByStatus('PENDING') }}</span></small>
                  </div>
                </div>
                <div class="col-md-3 col-6 mb-2">
                  <div class="d-flex align-items-center">
                    <div class="legend-color-box bg-danger me-2"></div>
                    <small><strong>Rejected</strong> <span class="badge bg-danger ms-1">{{ getLeaveCountByStatus('REJECTED') }}</span></small>
                  </div>
                </div>
                <div class="col-md-3 col-6 mb-2">
                  <div class="d-flex align-items-center">
                    <div class="legend-color-box bg-secondary me-2"></div>
                    <small><strong>Cancelled</strong> <span class="badge bg-secondary ms-1">{{ getLeaveCountByStatus('CANCELLED') }}</span></small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Loading States -->
            <div *ngIf="isLoadingHolidays || isLoadingLeaves" class="mt-3 pt-3 border-top">
              <div class="d-flex align-items-center text-muted">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                <small>
                  Loading additional data
                  <span *ngIf="isLoadingHolidays">(holidays)</span>
                  <span *ngIf="isLoadingLeaves">(leaves)</span>...
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

<div id="fullCalModal" class="modal fade">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h4 id="modalTitle1" class="modal-title"></h4>
        <button type="button" class="btn-close" data-dismiss="modal"><span class="visually-hidden">close</span></button>
      </div>
      <div id="modalBody1" class="modal-body"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button class="btn btn-primary">Event Page</button>
      </div>
    </div>
  </div>
</div>

<div id="createEventModal" class="modal fade">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h4 id="modalTitle2" class="modal-title">Add event</h4>
        <button type="button" class="btn-close" data-dismiss="modal"><span class="visually-hidden">close</span></button>
      </div>
      <div id="modalBody2" class="modal-body">
        <form>
          <div class="mb-3">
            <label for="formGroupExampleInput" class="form-label">Example label</label>
            <input type="text" class="form-control" id="formGroupExampleInput" placeholder="Example input">
          </div>
          <div class="mb-3">
            <label for="formGroupExampleInput2" class="form-label">Another label</label>
            <input type="text" class="form-control" id="formGroupExampleInput2" placeholder="Another input">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button class="btn btn-primary">Add</button>
      </div>
    </div>
  </div>
</div>