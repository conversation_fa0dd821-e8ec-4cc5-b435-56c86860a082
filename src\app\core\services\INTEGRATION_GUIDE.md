# Permission System Integration Guide

## Overview
This guide explains how to integrate the new comprehensive permission system into your Angular application.

## 🔧 Required Integrations

### 1. App Module Configuration

Add the new services to your app module or main providers:

```typescript
// In app.config.ts or app.module.ts
import { PermissionService } from './core/services/permission.service';
import { PermissionErrorService } from './core/services/permission-error.service';
import { PermissionAuditService } from './core/services/permission-audit.service';
import { DynamicPermissionLoaderService } from './core/services/dynamic-permission-loader.service';
import { PermissionCacheService } from './core/services/permission-cache.service';
import { PermissionValidationInterceptor } from './core/interceptors/permission-validation.interceptor';

// Add to providers array:
providers: [
  PermissionService,
  PermissionErrorService,
  PermissionAuditService,
  DynamicPermissionLoaderService,
  PermissionCacheService,
  {
    provide: HTTP_INTERCEPTORS,
    useClass: PermissionValidationInterceptor,
    multi: true
  }
]
```

### 2. Add Permission Error Notification Component

Add the notification component to your main app component template:

```html
<!-- In app.component.html -->
<app-permission-error-notification></app-permission-error-notification>
<router-outlet></router-outlet>
```

### 3. Import Permission Directives

In components where you want to use permission directives:

```typescript
import { PERMISSION_DIRECTIVES } from './core/directives';

@Component({
  // ...
  imports: [CommonModule, ...PERMISSION_DIRECTIVES],
  // ...
})
```

### 4. Update AuthService Integration

Ensure your AuthService has the `updateCurrentUser` method:

```typescript
// In auth.service.ts
updateCurrentUser(user: any): void {
  this.currentUserSubject.next(user);
  // Update localStorage if needed
  localStorage.setItem('currentUser', JSON.stringify(user));
}
```

## 🎯 Usage Examples

### Template Permission Checks

```html
<!-- Hide/show elements based on permissions -->
<div *hasPermission="'users:read'">
  User content visible only with read permission
</div>

<!-- Multiple permissions (any) -->
<div *hasPermission="['users:read', 'users:write']">
  Content for users with any of these permissions
</div>

<!-- Multiple permissions (all required) -->
<div *hasPermission="['users:read', 'users:write']; requireAll: true">
  Content for users with ALL permissions
</div>

<!-- With else template -->
<div *hasPermission="'admin:access'; else noAccess">
  Admin content
</div>
<ng-template #noAccess>
  <p>You need admin access to view this content.</p>
</ng-template>

<!-- Disable buttons based on permissions -->
<button [disableIfNoPermission]="'users:create'">
  Create User
</button>

<!-- Using pipe -->
<button [disabled]="!('users:write' | hasPermission)">
  Edit User
</button>
```

### Component Permission Checks

```typescript
import { PermissionService } from './core/services/permission.service';

@Component({...})
export class MyComponent {
  constructor(private permissionService: PermissionService) {}

  // Simple permission check
  canEditUsers(): boolean {
    return this.permissionService.hasPermission('users:write');
  }

  // Multiple permissions check
  canManageUsers(): boolean {
    return this.permissionService.hasAnyPermission(['users:create', 'users:update', 'users:delete']);
  }

  // Permission check with error handling
  performAction(): void {
    try {
      this.permissionService.requirePermissions(
        'delete user',
        ['users:delete'],
        { component: 'MyComponent' }
      );
      
      // Perform the action
      this.deleteUser();
    } catch (error) {
      // Error is automatically handled by PermissionErrorService
      console.log('Permission denied');
    }
  }

  // Check with audit logging
  auditedAction(): void {
    const hasPermission = this.permissionService.checkWithAudit(
      'view sensitive data',
      ['admin:access'],
      false
    );
    
    if (hasPermission) {
      this.viewSensitiveData();
    }
  }
}
```

### API Permission Validation

The HTTP interceptor automatically validates permissions for API calls based on the endpoint configuration. To add new endpoints:

```typescript
import { PermissionValidationInterceptor } from './core/interceptors/permission-validation.interceptor';

// In your service or component
constructor(private interceptor: PermissionValidationInterceptor) {
  // Add custom endpoint configuration
  this.interceptor.addEndpointConfig({
    endpoint: '/api/v1/custom-endpoint',
    method: 'POST',
    requiredPermissions: ['custom:create'],
    description: 'Create custom resource'
  });
}
```

### Dynamic Permission Loading

```typescript
import { DynamicPermissionLoaderService } from './core/services/dynamic-permission-loader.service';

@Component({...})
export class MyComponent implements OnInit {
  constructor(private permissionLoader: DynamicPermissionLoaderService) {}

  ngOnInit(): void {
    // Subscribe to permission refresh events
    this.permissionLoader.permissionRefresh$.subscribe(result => {
      if (result?.success) {
        console.log('Permissions refreshed successfully');
        // Update UI accordingly
      }
    });
  }

  refreshPermissions(): void {
    this.permissionLoader.refreshPermissions().subscribe(result => {
      console.log('Manual refresh result:', result);
    });
  }
}
```

### Cache Management

```typescript
import { PermissionCacheService } from './core/services/permission-cache.service';

@Component({...})
export class AdminComponent {
  constructor(private cacheService: PermissionCacheService) {}

  clearUserCache(userId: string): void {
    this.cacheService.invalidateUserCache(userId);
  }

  clearAllPermissionCache(): void {
    this.cacheService.invalidatePermissionCache();
  }

  getCacheMetrics(): void {
    this.cacheService.metrics$.subscribe(metrics => {
      console.log('Cache hit rate:', metrics.hitRate + '%');
      console.log('Total entries:', metrics.totalEntries);
    });
  }
}
```

## 🔍 Monitoring and Debugging

### Audit Logs

```typescript
import { PermissionAuditService } from './core/services/permission-audit.service';

@Component({...})
export class AuditComponent {
  constructor(private auditService: PermissionAuditService) {}

  viewAuditStatistics(): void {
    this.auditService.getStatistics().subscribe(stats => {
      console.log('Total permission checks:', stats.totalChecks);
      console.log('Denied permissions:', stats.topDeniedPermissions);
    });
  }

  logSecurityEvent(): void {
    this.auditService.logSecurityEvent(
      'SUSPICIOUS_ACTIVITY',
      'Multiple failed permission attempts',
      'HIGH',
      { userId: 'user123', attempts: 5 }
    );
  }
}
```

## 🚨 Important Notes

1. **Circular Dependencies**: Avoid injecting PermissionService into AuthService to prevent circular dependencies.

2. **Performance**: The cache service helps reduce permission check overhead. Monitor cache hit rates in production.

3. **Security**: Remember that frontend permission checks are for UX only. Always validate permissions on the backend.

4. **Audit Compliance**: The audit service logs all permission checks. Ensure you have proper data retention policies.

5. **Error Handling**: The error service provides user-friendly messages. Customize them based on your application's needs.

## 🔄 Migration from Old System

1. Replace direct `authService.hasPermission()` calls with `permissionService.hasPermission()`
2. Update permission strings to follow the `{resource}:{action}` format
3. Add permission guards to routes that were missing them
4. Replace hardcoded permission checks with the new directive system
5. Test all permission scenarios thoroughly

## 📝 Testing

Create unit tests for your permission logic:

```typescript
describe('Permission System', () => {
  let permissionService: PermissionService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [PermissionService, ...]
    });
    permissionService = TestBed.inject(PermissionService);
  });

  it('should grant permission for valid user', () => {
    // Mock user with permissions
    spyOn(authService, 'currentUserValue').and.returnValue({
      permissions: ['users:read']
    });

    expect(permissionService.hasPermission('users:read')).toBe(true);
  });
});
```
