import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  BoardAffiliationService,
  BoardAffiliation,
  BoardAffiliationStatistics
} from '../../../../core/services/board-affiliation.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { BoardAffiliationFormComponent } from './board-affiliation-form/board-affiliation-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-board-affiliations',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective
  ],
  templateUrl: './board-affiliations.component.html',
  styleUrls: ['./board-affiliations.component.scss']
})
export class BoardAffiliationsComponent implements OnInit {
  // Data properties
  affiliations: BoardAffiliation[] = [];
  deletedAffiliations: BoardAffiliation[] = [];
  statistics: BoardAffiliationStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedType = '';
  selectedMembershipType = '';
  selectedCompensationType = '';
  selectedCountry = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedAffiliations: Set<string> = new Set();
  selectAll = false;

  // Filter options
  affiliationTypes: any[] = [];
  membershipTypes: any[] = [];
  compensationTypes: any[] = [];
  countries: string[] = [];

  constructor(
    private affiliationService: BoardAffiliationService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadAffiliations();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.affiliationTypes = this.affiliationService.getAffiliationTypes();
    this.membershipTypes = this.affiliationService.getMembershipTypes();
    this.compensationTypes = this.affiliationService.getCompensationTypes();
    this.countries = this.affiliationService.getCountryList();
  }

  /**
   * Load affiliations with current filters
   */
  loadAffiliations(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      type: this.selectedType || undefined,
      membership_type: this.selectedMembershipType || undefined,
      compensation_type: this.selectedCompensationType || undefined,
      country: this.selectedCountry || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.affiliationService.getAffiliations(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedAffiliations = response.data.filter(a => a.deleted_at);
            this.affiliations = [];
          } else {
            this.affiliations = response.data.filter(a => !a.deleted_at);
            this.deletedAffiliations = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load board affiliations';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load board affiliations. Please try again.'
        });
      }
    });
  }

  /**
   * Load affiliation statistics
   */
  loadStatistics(): void {
    this.affiliationService.getAffiliationStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search affiliations
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Filter by type
   */
  onTypeFilter(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Filter by membership type
   */
  onMembershipTypeFilter(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Filter by compensation type
   */
  onCompensationTypeFilter(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Filter by country
   */
  onCountryFilter(): void {
    this.currentPage = 1;
    this.loadAffiliations();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedAffiliations.clear();
    this.selectAll = false;
    
    if (mode !== 'statistics') {
      this.loadAffiliations();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadAffiliations();
  }

  /**
   * Open create affiliation modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(BoardAffiliationFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadAffiliations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Board affiliation created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit affiliation modal
   */
  openEditModal(affiliation: BoardAffiliation): void {
    const modalRef = this.modalService.open(BoardAffiliationFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.affiliation = { ...affiliation };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadAffiliations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Board affiliation updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete affiliation
   */
  deleteAffiliation(affiliation: BoardAffiliation): void {
    this.popupService.showConfirmation({
      title: 'Delete Board Affiliation',
      message: `Are you sure you want to delete "${affiliation.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.affiliationService.deleteAffiliation(affiliation.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadAffiliations();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Board affiliation deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete affiliation.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore affiliation
   */
  restoreAffiliation(affiliation: BoardAffiliation): void {
    this.popupService.showConfirmation({
      title: 'Restore Board Affiliation',
      message: `Are you sure you want to restore "${affiliation.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.affiliationService.restoreAffiliation(affiliation.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadAffiliations();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Board affiliation restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore affiliation.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadAffiliations();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Board affiliations uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.affiliationService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'board_affiliations_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadAffiliations();
    this.loadStatistics();
  }

  /**
   * Get affiliation type label
   */
  getAffiliationTypeLabel(type: string): string {
    return this.affiliationService.getAffiliationTypeLabel(type);
  }

  /**
   * Get membership type label
   */
  getMembershipTypeLabel(type: string): string {
    return this.affiliationService.getMembershipTypeLabel(type);
  }

  /**
   * Get compensation type label
   */
  getCompensationTypeLabel(type: string): string {
    return this.affiliationService.getCompensationTypeLabel(type);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): BoardAffiliation[] {
    return this.viewMode === 'deleted' ? this.deletedAffiliations : this.affiliations;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByAffiliationId(index: number, affiliation: BoardAffiliation): string {
    return affiliation.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
