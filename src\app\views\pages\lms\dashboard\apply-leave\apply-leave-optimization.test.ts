import { TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { ApplyLeaveComponent } from './apply-leave.component';
import { LeaveService } from '../../../../../core/services/leave.service';
import { EmployeeService } from '../../../../../core/services/employee.service';
import { EmployeeCacheService } from '../../../../../core/services/employee-cache.service';
import { CalendarService } from '../../../../../core/services/calendar.service';
import { AuthService } from '../../../../../core/services/auth.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangeDetectorRef } from '@angular/core';

describe('ApplyLeaveComponent - API Optimization Tests', () => {
  let component: ApplyLeaveComponent;
  let leaveService: jasmine.SpyObj<LeaveService>;
  let employeeService: jasmine.SpyObj<EmployeeService>;
  let employeeCacheService: jasmine.SpyObj<EmployeeCacheService>;
  let calendarService: jasmine.SpyObj<CalendarService>;
  let authService: jasmine.SpyObj<AuthService>;

  beforeEach(async () => {
    const leaveServiceSpy = jasmine.createSpyObj('LeaveService', [
      'getLeaveTypes', 'getMyLeaveBalance', 'getMyLeaves', 'testApiEndpoint'
    ]);
    const employeeServiceSpy = jasmine.createSpyObj('EmployeeService', [
      'getAllEmployees', 'getEmployeeByUuid'
    ]);
    const employeeCacheServiceSpy = jasmine.createSpyObj('EmployeeCacheService', [
      'getEmployees', 'getEmployeeNames', 'getCurrentEmployee', 'getEmployeeNamesByIds', 'preloadEmployees'
    ]);
    const calendarServiceSpy = jasmine.createSpyObj('CalendarService', [
      'getHolidays', 'testHolidayApi'
    ]);
    const authServiceSpy = jasmine.createSpyObj('AuthService', [], {
      currentUserValue: { id: 'test-uuid', email: '<EMAIL>' }
    });

    await TestBed.configureTestingModule({
      declarations: [ApplyLeaveComponent],
      providers: [
        { provide: LeaveService, useValue: leaveServiceSpy },
        { provide: EmployeeService, useValue: employeeServiceSpy },
        { provide: EmployeeCacheService, useValue: employeeCacheServiceSpy },
        { provide: CalendarService, useValue: calendarServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: NgbModal, useValue: jasmine.createSpyObj('NgbModal', ['open']) },
        { provide: ChangeDetectorRef, useValue: jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']) }
      ]
    }).compileComponents();

    const fixture = TestBed.createComponent(ApplyLeaveComponent);
    component = fixture.componentInstance;
    
    leaveService = TestBed.inject(LeaveService) as jasmine.SpyObj<LeaveService>;
    employeeService = TestBed.inject(EmployeeService) as jasmine.SpyObj<EmployeeService>;
    employeeCacheService = TestBed.inject(EmployeeCacheService) as jasmine.SpyObj<EmployeeCacheService>;
    calendarService = TestBed.inject(CalendarService) as jasmine.SpyObj<CalendarService>;
    authService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
  });

  describe('Parallel Loading Optimization', () => {
    it('should load all data in parallel instead of sequentially', (done) => {
      // Setup mock responses
      leaveService.getLeaveTypes.and.returnValue(of({ success: true, data: [] }));
      leaveService.getMyLeaveBalance.and.returnValue(of([]));
      calendarService.getHolidays.and.returnValue(of([]));
      employeeCacheService.getCurrentEmployee.and.returnValue(of(null));
      leaveService.testApiEndpoint.and.returnValue(of(true));
      calendarService.testHolidayApi.and.returnValue(of(true));

      // Track call order
      const callOrder: string[] = [];
      leaveService.getLeaveTypes.and.callFake(() => {
        callOrder.push('leaveTypes');
        return of({ success: true, data: [] });
      });
      leaveService.getMyLeaveBalance.and.callFake(() => {
        callOrder.push('leaveBalances');
        return of([]);
      });
      calendarService.getHolidays.and.callFake(() => {
        callOrder.push('holidays');
        return of([]);
      });

      // Call the optimized loading method
      (component as any).loadAllDataInParallel();

      // Verify all calls were made
      setTimeout(() => {
        expect(leaveService.getLeaveTypes).toHaveBeenCalled();
        expect(leaveService.getMyLeaveBalance).toHaveBeenCalled();
        expect(calendarService.getHolidays).toHaveBeenCalled();
        
        // All calls should be initiated immediately (parallel)
        expect(callOrder.length).toBe(3);
        done();
      }, 100);
    });

    it('should handle partial failures gracefully', (done) => {
      // Setup mixed success/failure responses
      leaveService.getLeaveTypes.and.returnValue(throwError('Leave types failed'));
      leaveService.getMyLeaveBalance.and.returnValue(of([]));
      calendarService.getHolidays.and.returnValue(of([]));
      employeeCacheService.getCurrentEmployee.and.returnValue(of(null));

      (component as any).loadAllDataInParallel();

      setTimeout(() => {
        // Should use fallback leave types
        expect(component.leaveTypes.length).toBeGreaterThan(0);
        expect(component.errorStates.leaveTypes).toBe('Failed to load leave types');
        expect(component.errorStates.leaveBalances).toBeNull();
        done();
      }, 100);
    });
  });

  describe('Employee Cache Service Integration', () => {
    it('should use cached employee data instead of repeated API calls', () => {
      const mockEmployees = [
        { id: '1', full_name: 'John Doe', employee_code: 'EMP001' },
        { id: '2', full_name: 'Jane Smith', employee_code: 'EMP002' }
      ];
      
      employeeCacheService.getEmployeeNamesByIds.and.returnValue(of({
        '1': 'John Doe',
        '2': 'Jane Smith'
      }));

      component.myLeaves = [
        { id: 1, employee_id: '1' } as any,
        { id: 2, employee_id: '2' } as any
      ];

      (component as any).fetchEmployeeNamesForLeaves();

      expect(employeeCacheService.getEmployeeNamesByIds).toHaveBeenCalledWith(['1', '2']);
      expect(employeeService.getEmployeeByUuid).not.toHaveBeenCalled();
    });

    it('should fallback to legacy method if cache fails', () => {
      employeeCacheService.getEmployeeNamesByIds.and.returnValue(throwError('Cache failed'));
      employeeService.getEmployeeByUuid.and.returnValue(of({ 
        success: true, 
        data: { first_name: 'John', last_name: 'Doe', employee_code: 'EMP001' }
      }));

      component.myLeaves = [{ id: 1, employee_id: '1' } as any];

      (component as any).fetchEmployeeNamesForLeaves();

      expect(employeeCacheService.getEmployeeNamesByIds).toHaveBeenCalled();
      // Legacy method should be called as fallback
      setTimeout(() => {
        expect(employeeService.getEmployeeByUuid).toHaveBeenCalledWith('1');
      }, 100);
    });
  });

  describe('Holiday Validation Caching', () => {
    it('should use cached holidays when available', () => {
      const mockHolidays = [
        { name: 'New Year', date: '2024-01-01' },
        { name: 'Christmas', date: '2024-12-25' }
      ];

      component.holidays = mockHolidays;
      component.holidaysLoaded = true;
      component.fromDate = '2024-01-01';
      component.toDate = '2024-01-02';
      component.selectedLeaveType = 'PL';

      (component as any).checkHolidayRestrictionWithFreshData();

      // Should not call API since holidays are cached
      expect(calendarService.getHolidays).not.toHaveBeenCalled();
      expect(component.hasHolidayRestriction).toBe(true);
    });

    it('should fetch fresh holidays when cache is empty', () => {
      component.holidays = [];
      component.holidaysLoaded = false;
      component.fromDate = '2024-01-01';
      component.toDate = '2024-01-02';
      component.selectedLeaveType = 'PL';

      calendarService.getHolidays.and.returnValue(of({
        success: true,
        data: { new_year_activities: [{ name: 'New Year', date: '2024-01-01' }] }
      }));

      (component as any).checkHolidayRestrictionWithFreshData();

      expect(calendarService.getHolidays).toHaveBeenCalled();
    });
  });

  describe('Loading States and Error Handling', () => {
    it('should track loading states for different sections', () => {
      expect(component.isLoading('leaveTypes')).toBe(false);
      expect(component.isLoading('leaveBalances')).toBe(false);
      expect(component.isLoading('holidays')).toBe(false);

      component.setAllLoadingStates(true);

      expect(component.isLoading('leaveTypes')).toBe(true);
      expect(component.isLoading('leaveBalances')).toBe(true);
      expect(component.isLoading('holidays')).toBe(true);
      expect(component.isAnyLoading).toBe(true);
    });

    it('should track error states for different sections', () => {
      expect(component.getError('leaveTypes')).toBeNull();
      expect(component.hasAnyErrors).toBe(false);

      component.errorStates.leaveTypes = 'Failed to load leave types';

      expect(component.getError('leaveTypes')).toBe('Failed to load leave types');
      expect(component.hasAnyErrors).toBe(true);
      expect(component.getCriticalErrors()).toContain('Leave Types');
    });

    it('should clear all errors and loading states', () => {
      component.errorStates.leaveTypes = 'Some error';
      component.loadingStates.leaveTypes = true;
      component.error = 'General error';

      component.clearAllErrors();

      expect(component.errorStates.leaveTypes).toBeNull();
      expect(component.error).toBeNull();
      expect(component.hasAnyErrors).toBe(false);
    });
  });

  describe('Lazy Loading', () => {
    it('should implement lazy loading for leave details', () => {
      spyOn(component as any, 'loadMyLeaves');
      
      (component as any).loadLeaveDetailsLazily();

      // Should defer loading
      expect((component as any).loadMyLeaves).not.toHaveBeenCalled();

      // Should load after timeout
      setTimeout(() => {
        expect((component as any).loadMyLeaves).toHaveBeenCalled();
      }, 150);
    });

    it('should check if leave details are loaded', () => {
      expect(component.areLeaveDetailsLoaded).toBe(false);

      component.leaveApplications = [{ id: 1 } as any];
      expect(component.areLeaveDetailsLoaded).toBe(true);

      component.leaveApplications = [];
      component.myLeaves = [{ id: 1 } as any];
      expect(component.areLeaveDetailsLoaded).toBe(true);
    });
  });
});
