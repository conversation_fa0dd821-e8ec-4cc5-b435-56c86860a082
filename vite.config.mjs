import { defineConfig } from 'vite';

export default defineConfig({
  // Suppress specific Vite warnings
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        // Suppress dynamic import warnings that cannot be analyzed by Vite
        if (warning.code === 'DYNAMIC_IMPORT' || 
            (warning.message && warning.message.includes('cannot be analyzed by Vite'))) {
          return;
        }
        // Use default for everything else
        warn(warning);
      }
    }
  },
  
  // Reduce log level to minimize console output
  logLevel: 'error',
  
  // Clear screen on rebuild
  clearScreen: false,
  
  // Server configuration
  server: {
    fs: {
      allow: ['..']
    },
    // Suppress HMR warnings
    hmr: {
      overlay: false
    }
  },
  
  // Suppress specific warnings in the console
  define: {
    'process.env.SUPPRESS_VITE_WARNINGS': 'true'
  }
});
