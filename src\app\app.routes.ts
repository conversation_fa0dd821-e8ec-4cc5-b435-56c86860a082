import { Routes } from '@angular/router';
import { BaseComponent } from './views/layout/base/base.component';
import { dynamicAuthGuard } from './core/guards/dynamic-auth.guard';

export const routes: Routes = [
  { path: '', redirectTo: 'auth/login', pathMatch: 'full' },
  { path: 'auth', loadChildren: () => import('./views/pages/auth/auth.routes')},  {
    path: '',
    component: BaseComponent,
    canActivateChild: [dynamicAuthGuard],
    children: [
      {
        path: 'dashboard',
        loadChildren: () => import('./views/pages/dashboard/dashboard.routes')
      },
      {
        path: 'employees',
        loadChildren: () => import('./views/pages/employee/employee.routes')
      },
      {
        path: 'sales-list',
        loadChildren: () => import('./views/pages/sales_process/sales.routes')
      },
      {
        path: 'admin-lead-list',
        loadChildren: () => import('./views/pages/admin-lead-list/admin-lead-list.routes')
      },
      {
        path: 'apps',
        loadChildren: () => import('./views/pages/apps/apps.routes')
      },
      {
        path: 'ui-components',
        loadChildren: () => import('./views/pages/ui-components/ui-components.routes')
      },
      {
        path: 'advanced-ui',
        loadChildren: () => import('./views/pages/advanced-ui/advanced-ui.routes')
      },
      {
        path: 'forms',
        loadChildren: () => import('./views/pages/forms/forms.routes')
      },
      {
        path: 'charts',
        loadChildren: () => import('./views/pages/charts/charts.routes')
      },
      {
        path: 'tables',
        loadChildren: () => import('./views/pages/tables/tables.routes')
      },
      {
        path: 'icons',
        loadChildren: () => import('./views/pages/icons/icons.routes')
      },
      {
        path: 'general',
        loadChildren: () => import('./views/pages/general/general.routes')
      },
      {
        path: 'master',
        loadChildren: () => import('./views/pages/master/master.routes')
      },
      {
        path: 'role-permission-management',
        loadChildren: () => import('./views/pages/role-permission-management/role-permission-management.routes')
      },
      {
        path: 'bucket',
        loadChildren: () => import('./views/pages/ops-teams/bucket/ops-team.routes')
      },
      {
        path: 'ops-team',
        redirectTo: 'bucket',
        pathMatch: 'full'
      },
      {
        path: 'login-at-institute',
        loadChildren: () => import('./views/pages/ops-teams/login-at-institute/login-at-institute.routes')
      },
      {
        path: 'disbursement',
        loadChildren: () => import('./views/pages/ops-teams/disbursement/disbursement.routes')
      },
      {
        path: 'lms',
        loadChildren: () => import('./views/pages/lms/lms.routes')
      }

    ]
  },
  {
    path: 'error',
    loadComponent: () => import('./views/pages/error/error.component').then(c => c.ErrorComponent),
  },
  {
    path: 'error/:type',
    loadComponent: () => import('./views/pages/error/error.component').then(c => c.ErrorComponent)
  },
  { path: '**', redirectTo: 'error/404', pathMatch: 'full' }
];
