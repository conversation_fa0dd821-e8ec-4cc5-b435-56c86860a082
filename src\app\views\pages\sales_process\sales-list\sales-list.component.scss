// Sales List Component Styles

// Modern table styling
.modern-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;

  thead {
    background-color: rgba(var(--bs-primary-rgb), 0.05);

    th {
      font-weight: 600;
      font-size: 0.8rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      // color: var(--bs-gray-700);
      padding: 12px 10px;
      border-top: none;
      border-bottom: 1px solid rgba(var(--bs-primary-rgb), 0.1);
      position: relative;
      cursor: pointer;
      transition: all 0.2s;

      &.asc:after, &.desc:after {
        content: '';
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
      }

      &.asc:after {
        border-bottom: 4px solid var(--bs-primary);
      }

      &.desc:after {
        border-top: 4px solid var(--bs-primary);
      }
    }
  }

  tbody {
    tr {
      transition: all 0.2s;

      &:hover {
        background-color: rgba(var(--bs-primary-rgb), 0.02);
      }

      td {
        vertical-align: middle;
        padding: 12px 10px;
        border-top: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
      }
    }
  }
}

// Connect With dropdown styling
#team_category {
  // Force dropdown to open downward
  position: relative;

  // Custom dropdown styling for better scroll behavior
  option {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  // For browsers that support it, limit dropdown height and add scroll
  &:focus {
    max-height: 200px;
    overflow-y: auto;
  }
}

// Alternative approach using CSS to ensure dropdown opens downward
.connect-with-dropdown {
  position: relative;

  select {
    // Ensure dropdown opens downward
    position: relative;
    z-index: 1;

    // Custom styling for better UX
    option {
      padding: 8px 12px;
      line-height: 1.4;
    }
  }

  // Force dropdown direction (browser-specific)
  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 0;
    pointer-events: none;
  }
}

// Multiple People Section Styling
.person-entry {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 1rem;
  margin-bottom: 1rem !important;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0 !important;
  }

  .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;

    &:hover {
      background-color: #dc3545;
      color: white;
    }
  }
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;

  h6 {
    color: #3F828B;
    font-weight: 600;
  }
}
