# HR Admin Templates

This folder contains template files for HR Admin functionality.

## Available Templates

### Employee Management
- `employee-bulk-upload-template.xlsx` - Template for bulk employee upload
- `employee-export-template.xlsx` - Template for employee data export

### Leave Management  
- `leave-application-template.xlsx` - Template for bulk leave applications
- `leave-balance-template.xlsx` - Template for leave balance import

### Attendance Management
- `attendance-import-template.xlsx` - Template for attendance data import
- `attendance-report-template.xlsx` - Template for attendance reports

### Payroll Management
- `salary-import-template.xlsx` - Template for salary data import
- `payroll-report-template.xlsx` - Template for payroll reports

## Template Structure

Each Excel template should include:
1. **Header Row**: Column names in the first row
2. **Data Validation**: Dropdown lists for predefined values
3. **Sample Data**: 1-2 rows of example data
4. **Instructions Sheet**: Detailed instructions for filling the template
5. **Format Guidelines**: Date formats, text limits, required fields

## Usage

Templates are downloaded when users click the "Download Template" button in the respective HR Admin sections.

## File Naming Convention

- Use kebab-case for file names
- Include the purpose in the name (import/export/report)
- Use `.xlsx` extension for Excel files
- Use `.csv` extension for CSV files

## Template Updates

When updating templates:
1. Update the file in this folder
2. Update the download service to reference the new file
3. Test the template with sample data
4. Update documentation if column structure changes
