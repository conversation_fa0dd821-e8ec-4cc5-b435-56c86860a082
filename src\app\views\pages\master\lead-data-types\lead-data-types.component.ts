import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import {
  LeadDataTypeService,
  LeadDataType,
  LeadDataTypeStatistics
} from '../../../../core/services/lead-data-type.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { LeadDataTypeFormComponent } from './lead-data-type-form/lead-data-type-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-lead-data-types',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule
  ],
  templateUrl: './lead-data-types.component.html',
  styleUrls: ['./lead-data-types.component.scss']
})
export class LeadDataTypesComponent implements OnInit {
  // Data properties
  leadDataTypes: LeadDataType[] = [];
  deletedLeadDataTypes: LeadDataType[] = [];
  statistics: LeadDataTypeStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedFieldType = '';
  selectedDataCategory = '';
  selectedRequired: 'all' | 'required' | 'optional' = 'all';
  selectedPII: 'all' | 'pii' | 'non_pii' = 'all';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedLeadDataTypes: Set<string> = new Set();
  selectAll = false;

  // Filter options
  fieldTypes: any[] = [];
  dataCategories: any[] = [];

  constructor(
    private leadDataTypeService: LeadDataTypeService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadLeadDataTypes();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.fieldTypes = this.leadDataTypeService.getFieldTypes();
    this.dataCategories = this.leadDataTypeService.getDataCategories();
  }

  /**
   * Load lead data types with current filters
   */
  loadLeadDataTypes(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      field_type: this.selectedFieldType || undefined,
      data_category: this.selectedDataCategory || undefined,
      is_required: this.selectedRequired === 'all' ? undefined : this.selectedRequired === 'required',
      is_pii: this.selectedPII === 'all' ? undefined : this.selectedPII === 'pii',
      include_deleted: this.viewMode === 'deleted'
    };

    this.leadDataTypeService.getLeadDataTypesWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedLeadDataTypes = response.data.filter(ldt => ldt.deleted_at);
            this.leadDataTypes = [];
          } else {
            this.leadDataTypes = response.data.filter(ldt => !ldt.deleted_at);
            this.deletedLeadDataTypes = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load lead data types';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load lead data types. Please try again.'
        });
      }
    });
  }

  /**
   * Load lead data type statistics
   */
  loadStatistics(): void {
    this.leadDataTypeService.getLeadDataTypeStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search lead data types
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Filter by field type
   */
  onFieldTypeFilter(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Filter by data category
   */
  onDataCategoryFilter(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Filter by required status
   */
  onRequiredFilter(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Filter by PII status
   */
  onPIIFilter(): void {
    this.currentPage = 1;
    this.loadLeadDataTypes();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedLeadDataTypes.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadLeadDataTypes();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadLeadDataTypes();
  }

  /**
   * Open create lead data type modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(LeadDataTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLeadDataTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Lead data type created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit lead data type modal
   */
  openEditModal(leadDataType: LeadDataType): void {
    const modalRef = this.modalService.open(LeadDataTypeFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.leadDataType = { ...leadDataType };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadLeadDataTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Lead data type updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete lead data type
   */
  deleteLeadDataType(leadDataType: LeadDataType): void {
    this.popupService.showConfirmation({
      title: 'Delete Lead Data Type',
      message: `Are you sure you want to delete "${leadDataType.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.leadDataTypeService.deleteLeadDataType(leadDataType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLeadDataTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Lead data type deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete lead data type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore lead data type
   */
  restoreLeadDataType(leadDataType: LeadDataType): void {
    this.popupService.showConfirmation({
      title: 'Restore Lead Data Type',
      message: `Are you sure you want to restore "${leadDataType.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.leadDataTypeService.restoreLeadDataType(leadDataType.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadLeadDataTypes();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Lead data type restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore lead data type.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadLeadDataTypes();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Lead data types uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.leadDataTypeService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'lead_data_types_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Generate form schema
   */
  generateFormSchema(): void {
    const selectedIds = Array.from(this.selectedLeadDataTypes);
    if (selectedIds.length === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select at least one data type to generate a form schema.'
      });
      return;
    }

    this.leadDataTypeService.generateFormSchema(selectedIds).subscribe({
      next: (response) => {
        if (response.success) {
          // Display the schema in a modal or download it
          const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          link.download = 'form_schema.json';
          link.click();
          window.URL.revokeObjectURL(url);

          this.popupService.showSuccess({
            title: 'Schema Generated',
            message: 'Form schema has been generated and downloaded successfully.',
            timer: 3000
          });
        } else {
          this.popupService.showError({
            title: 'Generation Failed',
            message: response.error || 'Failed to generate form schema.'
          });
        }
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Generation Failed',
          message: error.message
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadLeadDataTypes();
    this.loadStatistics();
  }

  /**
   * Get field type label
   */
  getFieldTypeLabel(fieldType: string): string {
    return this.leadDataTypeService.getFieldTypeLabel(fieldType);
  }

  /**
   * Get data category label
   */
  getDataCategoryLabel(category: string): string {
    return this.leadDataTypeService.getDataCategoryLabel(category);
  }

  /**
   * Get data category color
   */
  getDataCategoryColor(category: string): string {
    return this.leadDataTypeService.getDataCategoryColor(category);
  }

  /**
   * Get field type badge class
   */
  getFieldTypeBadgeClass(fieldType: string): string {
    return this.leadDataTypeService.getFieldTypeBadgeClass(fieldType);
  }

  /**
   * Get category badge class
   */
  getCategoryBadgeClass(category: string): string {
    return this.leadDataTypeService.getCategoryBadgeClass(category);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get validation rules summary
   */
  getValidationRulesSummary(leadDataType: LeadDataType): string {
    return this.leadDataTypeService.getValidationRulesSummary(leadDataType);
  }

  /**
   * Toggle selection of a lead data type
   */
  toggleSelection(id: string): void {
    if (this.selectedLeadDataTypes.has(id)) {
      this.selectedLeadDataTypes.delete(id);
    } else {
      this.selectedLeadDataTypes.add(id);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    if (this.selectAll) {
      // Select all
      this.getCurrentList().forEach(dataType => {
        this.selectedLeadDataTypes.add(dataType.id);
      });
    } else {
      // Deselect all
      this.getCurrentList().forEach(dataType => {
        this.selectedLeadDataTypes.delete(dataType.id);
      });
    }
  }

  /**
   * Update select all state based on current selections
   */
  updateSelectAllState(): void {
    const currentList = this.getCurrentList();
    this.selectAll = currentList.length > 0 &&
      currentList.every(dataType => this.selectedLeadDataTypes.has(dataType.id));
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): LeadDataType[] {
    return this.viewMode === 'deleted' ? this.deletedLeadDataTypes : this.leadDataTypes;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByLeadDataTypeId(index: number, leadDataType: LeadDataType): string {
    return leadDataType.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
