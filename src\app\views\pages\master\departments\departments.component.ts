import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  DepartmentService,
  Department,
  DepartmentStatistics,
  DepartmentTree
} from '../../../../core/services/department.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { DepartmentFormComponent } from './department-form/department-form.component';
import { DepartmentHierarchyComponent } from './department-hierarchy/department-hierarchy.component';

@Component({
  selector: 'app-departments',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective
  ],
  templateUrl: './departments.component.html',
  styleUrls: ['./departments.component.scss']
})
export class DepartmentsComponent implements OnInit {
  // Data properties
  departments: Department[] = [];
  departmentTree: DepartmentTree[] = [];
  statistics: DepartmentStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'list' | 'tree' | 'statistics' = 'list';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedParent = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedDepartments: Set<string> = new Set();
  selectAll = false;

  constructor(
    private departmentService: DepartmentService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadDepartments();
    //this.loadStatistics();
  }

  /**
   * Load departments with current filters
   */
  loadDepartments(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      parent_id: this.selectedParent || undefined
    };

    this.departmentService.getDepartments(params).subscribe({
      next: (response) => {
        if (response.success) {
          this.departments = response.data;
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load departments';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load departments. Please try again.'
        });
      }
    });
  }

  /**
   * Load department tree structure
   */
  loadDepartmentTree(): void {
    this.loading = true;

    this.departmentService.getDepartmentTree().subscribe({
      next: (response) => {
        if (response.success) {
          this.departmentTree = response.data;
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load department tree.'
        });
      }
    });
  }

  /**
   * Load department statistics
   */
  loadStatistics(): void {
    this.departmentService.getDepartmentStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search departments
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadDepartments();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadDepartments();
  }

  /**
   * Filter by parent department
   */
  onParentFilter(): void {
    this.currentPage = 1;
    this.loadDepartments();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'list' | 'tree' | 'statistics'): void {
    this.viewMode = mode;

    if (mode === 'tree' && this.departmentTree.length === 0) {
      this.loadDepartmentTree();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadDepartments();
  }

  /**
   * Open create department modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(DepartmentFormComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;
    modalRef.componentInstance.parentDepartments = this.departments.filter(d => d.is_active);

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadDepartments();
        //this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Department created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit department modal
   */
  openEditModal(department: Department): void {
    const modalRef = this.modalService.open(DepartmentFormComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.department = { ...department };
    modalRef.componentInstance.parentDepartments = this.departments.filter(d =>
      d.is_active && d.id !== department.id
    );

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadDepartments();
        //this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Department updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete department
   */
  deleteDepartment(department: Department): void {
    this.popupService.showConfirmation({
      title: 'Delete Department',
      message: `Are you sure you want to delete "${department.name}"? This action cannot be undone.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.departmentService.deleteDepartment(department.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadDepartments();
              //this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Department deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete department.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * View department hierarchy
   */
  viewHierarchy(department: Department): void {
    const modalRef = this.modalService.open(DepartmentHierarchyComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.departmentId = department.id;
    modalRef.componentInstance.departmentName = department.name;
  }

  /**
   * Toggle department selection
   */
  toggleSelection(departmentId: string): void {
    if (this.selectedDepartments.has(departmentId)) {
      this.selectedDepartments.delete(departmentId);
    } else {
      this.selectedDepartments.add(departmentId);
    }
    this.updateSelectAllState();
  }

  /**
   * Toggle select all
   */
  toggleSelectAll(): void {
    if (this.selectAll) {
      this.selectedDepartments.clear();
    } else {
      this.departments.forEach(dept => this.selectedDepartments.add(dept.id));
    }
    this.selectAll = !this.selectAll;
  }

  /**
   * Update select all state
   */
  private updateSelectAllState(): void {
    this.selectAll = this.departments.length > 0 &&
      this.departments.every(dept => this.selectedDepartments.has(dept.id));
  }

  /**
   * Bulk delete selected departments
   */
  bulkDelete(): void {
    if (this.selectedDepartments.size === 0) {
      this.popupService.showWarning({
        title: 'No Selection',
        message: 'Please select departments to delete.'
      });
      return;
    }

    this.popupService.showConfirmation({
      title: 'Bulk Delete',
      message: `Are you sure you want to delete ${this.selectedDepartments.size} selected departments?`,
      confirmText: 'Yes, Delete All',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        // Implementation for bulk delete would go here
        this.popupService.showInfo({
          title: 'Feature Coming Soon',
          message: 'Bulk delete functionality will be implemented in the next update.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadDepartments();
    //this.loadStatistics();
    if (this.viewMode === 'tree') {
      this.loadDepartmentTree();
    }
  }

  /**
   * Get department level indicator
   */
  getLevelIndicator(level: number): string {
    return '—'.repeat(level);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Track by function for ngFor performance
   */
  trackByDepartmentId(index: number, department: Department): string {
    return department.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
