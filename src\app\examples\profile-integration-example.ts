/**
 * Profile Integration Example
 * 
 * This file demonstrates how to integrate the updated EmployeeService
 * with the profile component for BizzCorp Employee API functionality.
 */

import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { EmployeeService, Employee, Department, Designation, Role, EmployeeServiceError } from '../core/services/employee.service';
import { forkJoin, of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';

@Component({
  selector: 'app-profile-integration-example',
  template: `
    <div class="profile-container">
      <h2>Employee Profile</h2>
      
      <!-- Loading State -->
      <div *ngIf="loading" class="loading">
        <p>Loading employee profile...</p>
      </div>
      
      <!-- Error State -->
      <div *ngIf="error" class="error-message">
        <p>{{ error.message }}</p>
        <button (click)="retryLoad()">Retry</button>
      </div>
      
      <!-- Profile Form -->
      <form *ngIf="profileForm && !loading" [formGroup]="profileForm" (ngSubmit)="onSubmit()">
        <!-- Personal Information -->
        <div class="form-section">
          <h3>Personal Information</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label>Employee Code</label>
              <input type="text" formControlName="employee_code" readonly>
            </div>
            
            <div class="form-group">
              <label>First Name</label>
              <input type="text" formControlName="first_name" required>
            </div>
            
            <div class="form-group">
              <label>Middle Name</label>
              <input type="text" formControlName="middle_name">
            </div>
            
            <div class="form-group">
              <label>Last Name</label>
              <input type="text" formControlName="last_name" required>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>Date of Birth</label>
              <input type="date" formControlName="date_of_birth">
            </div>
            
            <div class="form-group">
              <label>Gender</label>
              <select formControlName="gender">
                <option value="">Select Gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Marital Status</label>
              <select formControlName="marital_status">
                <option value="">Select Status</option>
                <option value="Single">Single</option>
                <option value="Married">Married</option>
                <option value="Divorced">Divorced</option>
                <option value="Widowed">Widowed</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Contact Information -->
        <div class="form-section">
          <h3>Contact Information</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label>Personal Email</label>
              <input type="email" formControlName="personal_email">
            </div>
            
            <div class="form-group">
              <label>Office Email</label>
              <input type="email" formControlName="office_email">
            </div>
            
            <div class="form-group">
              <label>Alternate Number</label>
              <input type="text" formControlName="alternet_no">
            </div>
          </div>
          
          <div class="form-group">
            <label>Address</label>
            <textarea formControlName="address" rows="3"></textarea>
          </div>
        </div>
        
        <!-- Work Information -->
        <div class="form-section">
          <h3>Work Information</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label>Department</label>
              <select formControlName="department_id">
                <option value="">Select Department</option>
                <option *ngFor="let dept of departments" [value]="dept.id">
                  {{ dept.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Designation</label>
              <select formControlName="designation_id">
                <option value="">Select Designation</option>
                <option *ngFor="let desig of designations" [value]="desig.id">
                  {{ desig.name }}
                </option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Role</label>
              <input type="text" formControlName="role">
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label>Joining Date</label>
              <input type="date" formControlName="joining_date">
            </div>
            
            <div class="form-group">
              <label>Reporting Date</label>
              <input type="date" formControlName="reporting_date">
            </div>
            
            <div class="form-group">
              <label>Office Location</label>
              <input type="text" formControlName="office_location">
            </div>
          </div>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
          <button type="button" (click)="resetForm()">Reset</button>
          <button type="submit" [disabled]="profileForm.invalid || saving">
            {{ saving ? 'Saving...' : 'Save Profile' }}
          </button>
        </div>
      </form>
    </div>
  `
})
export class ProfileIntegrationExample implements OnInit {
  profileForm: FormGroup;
  employee: Employee | null = null;
  departments: Department[] = [];
  designations: Designation[] = [];
  roles: Role[] = [];
  
  loading = false;
  saving = false;
  error: EmployeeServiceError | null = null;
  
  // Example employee ID - in real app, this would come from route params or auth service
  private readonly EMPLOYEE_ID = '1027';

  constructor(
    private employeeService: EmployeeService,
    private fb: FormBuilder
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadProfileData();
  }

  private initializeForm(): void {
    this.profileForm = this.fb.group({
      employee_code: ['', Validators.required],
      first_name: ['', Validators.required],
      middle_name: [''],
      last_name: ['', Validators.required],
      date_of_birth: [''],
      gender: [''],
      marital_status: [''],
      personal_email: ['', [Validators.email]],
      office_email: ['', [Validators.email]],
      alternet_no: [''],
      address: [''],
      department_id: [''],
      designation_id: [''],
      role: [''],
      joining_date: [''],
      reporting_date: [''],
      office_location: ['']
    });
  }

  private loadProfileData(): void {
    this.loading = true;
    this.error = null;

    // Load all required data in parallel
    forkJoin({
      employee: this.employeeService.getEmployeeProfile(this.EMPLOYEE_ID),
      departments: this.employeeService.getDepartmentsMasterData(),
      designations: this.employeeService.getDesignationsMasterData()
    }).pipe(
      catchError(error => {
        console.error('Error loading profile data:', error);
        this.error = error;
        return of(null);
      }),
      finalize(() => {
        this.loading = false;
      })
    ).subscribe(result => {
      if (result) {
        this.employee = result.employee;
        this.departments = result.departments;
        this.designations = result.designations;
        
        // Populate form with employee data
        this.populateForm(result.employee);
        
        // Load role details if sub_role_id exists
        if (result.employee.sub_role_id) {
          this.loadRoleDetails(result.employee.sub_role_id);
        }
      }
    });
  }

  private populateForm(employee: Employee): void {
    this.profileForm.patchValue({
      employee_code: employee.employee_code,
      first_name: employee.first_name,
      middle_name: employee.middle_name || '',
      last_name: employee.last_name,
      date_of_birth: employee.date_of_birth,
      gender: employee.gender,
      marital_status: employee.marital_status,
      personal_email: employee.personal_email,
      office_email: employee.office_email,
      alternet_no: employee.alternet_no,
      address: employee.address,
      department_id: employee.department_id,
      designation_id: employee.designation_id,
      role: employee.role,
      joining_date: employee.joining_date,
      reporting_date: employee.reporting_date,
      office_location: employee.office_location
    });
  }

  private loadRoleDetails(roleId: string): void {
    this.employeeService.getRoleById(roleId).subscribe({
      next: (role) => {
        console.log('Role details loaded:', role);
        // You can use role details for additional functionality
      },
      error: (error) => {
        console.warn('Could not load role details:', error);
        // Non-critical error, don't show to user
      }
    });
  }

  onSubmit(): void {
    if (this.profileForm.valid) {
      this.saving = true;
      const formData = this.profileForm.value;
      
      console.log('Saving profile data:', formData);
      
      // Here you would call an update method
      // this.employeeService.updateEmployeeProfile(this.EMPLOYEE_ID, formData)
      
      // Simulate save operation
      setTimeout(() => {
        this.saving = false;
        console.log('Profile saved successfully');
      }, 2000);
    }
  }

  resetForm(): void {
    if (this.employee) {
      this.populateForm(this.employee);
    }
  }

  retryLoad(): void {
    this.loadProfileData();
  }
}
