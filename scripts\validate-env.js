#!/usr/bin/env node

/**
 * Environment Validation Script for BizzCorp Frontend
 *
 * This script validates that all required environment variables are properly configured
 * and checks for any remaining hardcoded URLs in the codebase.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateEnvironmentFiles() {
  log('\n🔍 Validating Environment Files...', 'cyan');

  const envFiles = [
    'src/environments/environment.ts',
    'src/environments/environment.prod.ts'
  ];

  let allValid = true;

  envFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');

      // Check if apiUrl is properly configured
      if (content.includes('apiUrl:')) {
        log(`✅ ${file} - apiUrl configured`, 'green');
      } else {
        log(`❌ ${file} - apiUrl not found`, 'red');
        allValid = false;
      }

      // Check for hardcoded URLs
      const hardcodedUrls = content.match(/https?:\/\/[^\s'"]+/g);
      if (hardcodedUrls && hardcodedUrls.length > 0) {
        log(`⚠️  ${file} - Found hardcoded URLs: ${hardcodedUrls.join(', ')}`, 'yellow');
      }
    } else {
      log(`❌ ${file} - File not found`, 'red');
      allValid = false;
    }
  });

  return allValid;
}

function validateDirectApiConfiguration() {
  log('\n🔍 Validating Direct API Configuration...', 'cyan');

  log('✅ Using direct API calls (no proxy configuration)', 'green');
  log('⚠️  Ensure your backend API server has CORS configured for:', 'yellow');
  log('   - http://localhost:4200 (development)', 'yellow');
  log('   - http://localhost:8020 (docker)', 'yellow');
  log('   - https://bizzcorp.antllp.com (production)', 'yellow');

  return true;
}

function validateDockerConfiguration() {
  log('\n🔍 Validating Docker Configuration...', 'cyan');

  let allValid = true;

  // Check docker-compose file
  if (fs.existsSync('docker-compose-bizzcorp.yml')) {
    const content = fs.readFileSync('docker-compose-bizzcorp.yml', 'utf8');

    if (content.includes('API_BASE_URL')) {
      log('✅ docker-compose-bizzcorp.yml - API_BASE_URL environment variable configured', 'green');
    } else {
      log('❌ docker-compose-bizzcorp.yml - API_BASE_URL environment variable not found', 'red');
      allValid = false;
    }
  } else {
    log('❌ docker-compose-bizzcorp.yml - File not found', 'red');
    allValid = false;
  }

  // Check nginx configuration
  if (fs.existsSync('nginx.conf')) {
    const content = fs.readFileSync('nginx.conf', 'utf8');

    if (content.includes('${API_BASE_URL}')) {
      log('✅ nginx.conf - Environment variable substitution configured', 'green');
    } else {
      log('❌ nginx.conf - Environment variable substitution not found', 'red');
      allValid = false;
    }
  } else {
    log('❌ nginx.conf - File not found', 'red');
    allValid = false;
  }

  // Check entrypoint script
  if (fs.existsSync('docker-entrypoint.sh')) {
    const content = fs.readFileSync('docker-entrypoint.sh', 'utf8');

    if (content.includes('envsubst')) {
      log('✅ docker-entrypoint.sh - Environment substitution script configured', 'green');
    } else {
      log('❌ docker-entrypoint.sh - Environment substitution not configured', 'red');
      allValid = false;
    }
  } else {
    log('❌ docker-entrypoint.sh - File not found', 'red');
    allValid = false;
  }

  return allValid;
}

function validatePackageScripts() {
  log('\n🔍 Validating Package Scripts...', 'cyan');

  if (fs.existsSync('package.json')) {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};

    const expectedScripts = [
      'start',
      'start:dev',
      'start:prod'
    ];

    let allValid = true;

    expectedScripts.forEach(script => {
      if (scripts[script]) {
        log(`✅ package.json - Script '${script}' configured`, 'green');
      } else {
        log(`❌ package.json - Script '${script}' not found`, 'red');
        allValid = false;
      }
    });

    return allValid;
  } else {
    log('❌ package.json - File not found', 'red');
    return false;
  }
}

function main() {
  log('🚀 BizzCorp Frontend Environment Validation', 'magenta');
  log('=' .repeat(50), 'magenta');

  const validations = [
    validateEnvironmentFiles(),
    validateDirectApiConfiguration(),
    validateDockerConfiguration(),
    validatePackageScripts()
  ];

  const allValid = validations.every(v => v);

  log('\n📊 Validation Summary', 'cyan');
  log('=' .repeat(30), 'cyan');

  if (allValid) {
    log('✅ All validations passed! Environment is properly configured.', 'green');
    log('\n🎉 Your application is ready for deployment with environment variables!', 'green');
  } else {
    log('❌ Some validations failed. Please fix the issues above.', 'red');
    log('\n📖 Check ENVIRONMENT_SETUP.md for detailed configuration instructions.', 'yellow');
    process.exit(1);
  }
}

// Run the validation
main();
