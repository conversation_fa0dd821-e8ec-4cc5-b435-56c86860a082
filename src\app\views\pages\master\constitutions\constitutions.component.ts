import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgbModal, NgbPaginationModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FeatherIconDirective } from '../../../../core/feather-icon/feather-icon.directive';
import {
  ConstitutionService,
  Constitution,
  ConstitutionStatistics
} from '../../../../core/services/constitution.service';
import { PopupDesignService } from '../../../../core/services/popup-design.service';
import { ConstitutionFormComponent } from './constitution-form/constitution-form.component';
import { BulkUploadComponent } from './bulk-upload/bulk-upload.component';

@Component({
  selector: 'app-constitutions',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    NgbPaginationModule,
    NgbTooltipModule,
    FeatherIconDirective
  ],
  templateUrl: './constitutions.component.html',
  styleUrls: ['./constitutions.component.scss']
})
export class ConstitutionsComponent implements OnInit {
  // Data properties
  constitutions: Constitution[] = [];
  deletedConstitutions: Constitution[] = [];
  statistics: ConstitutionStatistics | null = null;

  // UI state
  loading = false;
  error: string | null = null;
  viewMode: 'active' | 'deleted' | 'statistics' = 'active';

  // Search and filtering
  searchTerm = '';
  selectedStatus: 'all' | 'active' | 'inactive' = 'all';
  selectedType = '';
  selectedComplianceStatus = '';
  selectedJurisdiction = '';
  selectedRegulatoryBody = '';

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;

  // Selection
  selectedConstitutions: Set<string> = new Set();
  selectAll = false;

  // Filter options
  constitutionTypes: any[] = [];
  complianceStatuses: any[] = [];
  jurisdictions: string[] = [];
  regulatoryBodies: string[] = [];

  constructor(
    private constitutionService: ConstitutionService,
    private popupService: PopupDesignService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadFilterOptions();
    this.loadConstitutions();
    this.loadStatistics();
  }

  /**
   * Load filter options
   */
  loadFilterOptions(): void {
    this.constitutionTypes = this.constitutionService.getConstitutionTypes();
    this.complianceStatuses = this.constitutionService.getComplianceStatuses();
    this.jurisdictions = this.constitutionService.getJurisdictionList();
    this.regulatoryBodies = this.constitutionService.getRegulatoryBodies();
  }

  /**
   * Load constitutions with current filters
   */
  loadConstitutions(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage,
      per_page: this.pageSize,
      search: this.searchTerm || undefined,
      is_active: this.selectedStatus === 'all' ? undefined : this.selectedStatus === 'active',
      type: this.selectedType || undefined,
      compliance_status: this.selectedComplianceStatus || undefined,
      jurisdiction: this.selectedJurisdiction || undefined,
      regulatory_body: this.selectedRegulatoryBody || undefined,
      include_deleted: this.viewMode === 'deleted'
    };

    this.constitutionService.getConstitutionsWithResponse(params).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.viewMode === 'deleted') {
            this.deletedConstitutions = response.data.filter(c => c.deleted_at);
            this.constitutions = [];
          } else {
            this.constitutions = response.data.filter(c => !c.deleted_at);
            this.deletedConstitutions = [];
          }
          this.totalItems = response.meta?.pagination?.total || 0;
        } else {
          this.error = response.error || 'Failed to load constitutions';
        }
        this.loading = false;
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.error = error.message;
        this.loading = false;
        this.cdr.markForCheck();
        this.popupService.showError({
          title: 'Loading Error',
          message: 'Failed to load constitutions. Please try again.'
        });
      }
    });
  }

  /**
   * Load constitution statistics
   */
  loadStatistics(): void {
    this.constitutionService.getConstitutionStatistics().subscribe({
      next: (response) => {
        if (response.success) {
          this.statistics = response.data;
          this.cdr.markForCheck();
        }
      },
      error: (error) => {
        console.error('Failed to load statistics:', error);
      }
    });
  }

  /**
   * Search constitutions
   */
  onSearch(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Filter by status
   */
  onStatusFilter(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Filter by type
   */
  onTypeFilter(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Filter by compliance status
   */
  onComplianceStatusFilter(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Filter by jurisdiction
   */
  onJurisdictionFilter(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Filter by regulatory body
   */
  onRegulatoryBodyFilter(): void {
    this.currentPage = 1;
    this.loadConstitutions();
  }

  /**
   * Change view mode
   */
  setViewMode(mode: 'active' | 'deleted' | 'statistics'): void {
    this.viewMode = mode;
    this.currentPage = 1;
    this.selectedConstitutions.clear();
    this.selectAll = false;

    if (mode !== 'statistics') {
      this.loadConstitutions();
    }
  }

  /**
   * Page change handler
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadConstitutions();
  }

  /**
   * Open create constitution modal
   */
  openCreateModal(): void {
    const modalRef = this.modalService.open(ConstitutionFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = false;

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadConstitutions();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Constitution created successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Open edit constitution modal
   */
  openEditModal(constitution: Constitution): void {
    const modalRef = this.modalService.open(ConstitutionFormComponent, {
      size: 'xl',
      backdrop: 'static'
    });

    modalRef.componentInstance.isEditMode = true;
    modalRef.componentInstance.constitution = { ...constitution };

    modalRef.result.then((result) => {
      if (result === 'saved') {
        this.loadConstitutions();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Success!',
          message: 'Constitution updated successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Delete constitution
   */
  deleteConstitution(constitution: Constitution): void {
    this.popupService.showConfirmation({
      title: 'Delete Constitution',
      message: `Are you sure you want to delete "${constitution.name}"? This action can be undone later.`,
      confirmText: 'Yes, Delete',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.constitutionService.deleteConstitution(constitution.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadConstitutions();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Deleted!',
                message: 'Constitution deleted successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Delete Failed',
                message: response.error || 'Failed to delete constitution.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Delete Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Restore constitution
   */
  restoreConstitution(constitution: Constitution): void {
    this.popupService.showConfirmation({
      title: 'Restore Constitution',
      message: `Are you sure you want to restore "${constitution.name}"?`,
      confirmText: 'Yes, Restore',
      cancelText: 'Cancel'
    }).then((result) => {
      if (result.isConfirmed) {
        this.constitutionService.restoreConstitution(constitution.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.loadConstitutions();
              this.loadStatistics();
              this.popupService.showSuccess({
                title: 'Restored!',
                message: 'Constitution restored successfully.',
                timer: 3000
              });
            } else {
              this.popupService.showError({
                title: 'Restore Failed',
                message: response.error || 'Failed to restore constitution.'
              });
            }
          },
          error: (error) => {
            this.popupService.showError({
              title: 'Restore Failed',
              message: error.message
            });
          }
        });
      }
    });
  }

  /**
   * Open bulk upload modal
   */
  openBulkUploadModal(): void {
    const modalRef = this.modalService.open(BulkUploadComponent, {
      size: 'lg',
      backdrop: 'static'
    });

    modalRef.result.then((result) => {
      if (result === 'uploaded') {
        this.loadConstitutions();
        this.loadStatistics();
        this.popupService.showSuccess({
          title: 'Upload Complete!',
          message: 'Constitutions uploaded successfully.',
          timer: 3000
        });
      }
    }).catch(() => {
      // Modal dismissed
    });
  }

  /**
   * Download template
   */
  downloadTemplate(): void {
    this.constitutionService.downloadTemplate().subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = 'constitutions_template.xlsx';
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        this.popupService.showError({
          title: 'Download Failed',
          message: 'Failed to download template file.'
        });
      }
    });
  }

  /**
   * Refresh data
   */
  refresh(): void {
    this.loadConstitutions();
    this.loadStatistics();
  }

  /**
   * Get constitution type label
   */
  getConstitutionTypeLabel(type: string): string {
    return this.constitutionService.getConstitutionTypeLabel(type);
  }

  /**
   * Get compliance status label
   */
  getComplianceStatusLabel(status: string): string {
    return this.constitutionService.getComplianceStatusLabel(status);
  }

  /**
   * Get status badge class
   */
  getStatusBadgeClass(isActive: boolean): string {
    return isActive ? 'badge bg-success' : 'badge bg-secondary';
  }

  /**
   * Get status text
   */
  getStatusText(isActive: boolean): string {
    return isActive ? 'Active' : 'Inactive';
  }

  /**
   * Get compliance badge class
   */
  getComplianceBadgeClass(status: string): string {
    switch (status) {
      case 'compliant': return 'badge bg-success';
      case 'non_compliant': return 'badge bg-danger';
      case 'under_review': return 'badge bg-warning';
      case 'pending': return 'badge bg-secondary';
      default: return 'badge bg-light';
    }
  }

  /**
   * Get current list based on view mode
   */
  getCurrentList(): Constitution[] {
    return this.viewMode === 'deleted' ? this.deletedConstitutions : this.constitutions;
  }

  /**
   * Track by function for ngFor performance
   */
  trackByConstitutionId(index: number, constitution: Constitution): string {
    return constitution.id;
  }

  /**
   * Get object keys for template iteration
   */
  getObjectKeys(obj: any): string[] {
    return Object.keys(obj || {});
  }

  /**
   * Math utility for template
   */
  get Math() {
    return Math;
  }
}
